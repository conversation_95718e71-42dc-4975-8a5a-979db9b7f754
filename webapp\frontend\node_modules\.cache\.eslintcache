[{"C:\\CMS\\webapp\\frontend\\src\\index.js": "1", "C:\\CMS\\webapp\\frontend\\src\\App.js": "2", "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js": "3", "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js": "4", "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js": "5", "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js": "6", "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js": "7", "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js": "8", "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js": "9", "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js": "10", "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js": "11", "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js": "12", "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js": "13", "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js": "14", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js": "15", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js": "16", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js": "17", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js": "18", "C:\\CMS\\webapp\\frontend\\src\\config.js": "19", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js": "20", "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js": "21", "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx": "22", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js": "23", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js": "24", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js": "25", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js": "26", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js": "27", "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js": "28", "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js": "29", "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js": "30", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js": "31", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js": "32", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js": "33", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js": "34", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js": "35", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js": "36", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js": "37", "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js": "38", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js": "39", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js": "40", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js": "41", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js": "42", "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js": "43", "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js": "44", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js": "45", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js": "46", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js": "47", "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js": "48", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js": "49", "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js": "50", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js": "51", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js": "52", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx": "53", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx": "54", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx": "55", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx": "56", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js": "57", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js": "58", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js": "59", "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js": "60", "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js": "61", "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js": "62", "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js": "63", "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js": "64", "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js": "65", "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js": "66", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js": "67", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js": "68", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js": "69", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js": "70", "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js": "71", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js": "72", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js": "73", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js": "74", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js": "75", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js": "76", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js": "77", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js": "78", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js": "79", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js": "80", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js": "81", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js": "82", "C:\\CMS\\webapp\\frontend\\src\\pages\\comande\\ComandePage.js": "83", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeList.js": "84", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\TestComande.js": "85", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\AccessoRapidoComanda.js": "86", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\RapportiGenerali.js": "87", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\ProveDettagliate.js": "88", "C:\\CMS\\webapp\\frontend\\src\\services\\nonConformitaService.js": "89", "C:\\CMS\\webapp\\frontend\\src\\services\\rapportiGeneraliService.js": "90", "C:\\CMS\\webapp\\frontend\\src\\services\\proveDettagliateService.js": "91", "C:\\CMS\\webapp\\frontend\\src\\components\\Logo.js": "92", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaConCavi.js": "93", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SmartCaviFilter.js": "94", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ContextMenu.js": "95", "C:\\CMS\\webapp\\frontend\\src\\hooks\\useContextMenu.js": "96", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialog.js": "97", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialog.js": "98", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialogCompleto.js": "99", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialogCompleto.js": "100", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CreateCantiereDialog.js": "101", "C:\\CMS\\webapp\\frontend\\src\\services\\weatherService.js": "102", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaMultipla.js": "103", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ValidationResultsDialog.js": "104", "C:\\CMS\\webapp\\frontend\\src\\services\\comandeValidationService.js": "105", "C:\\CMS\\webapp\\frontend\\src\\services\\responsabiliService.js": "106", "C:\\CMS\\webapp\\frontend\\src\\components\\responsabili\\GestioneResponsabili.js": "107", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeListRivoluzionato.js": "108"}, {"size": 557, "mtime": 1746952718482, "results": "109", "hashOfConfig": "110"}, {"size": 3196, "mtime": 1748982170834, "results": "111", "hashOfConfig": "110"}, {"size": 996, "mtime": 1746970152489, "results": "112", "hashOfConfig": "110"}, {"size": 11085, "mtime": 1749414725518, "results": "113", "hashOfConfig": "110"}, {"size": 21191, "mtime": 1748751093271, "results": "114", "hashOfConfig": "110"}, {"size": 5121, "mtime": 1749188293819, "results": "115", "hashOfConfig": "110"}, {"size": 2216, "mtime": 1746640055487, "results": "116", "hashOfConfig": "110"}, {"size": 7394, "mtime": 1748034003517, "results": "117", "hashOfConfig": "110"}, {"size": 6749, "mtime": 1746282201800, "results": "118", "hashOfConfig": "110"}, {"size": 15637, "mtime": 1749188445361, "results": "119", "hashOfConfig": "110"}, {"size": 2455, "mtime": 1749188313610, "results": "120", "hashOfConfig": "110"}, {"size": 2050, "mtime": 1746647945415, "results": "121", "hashOfConfig": "110"}, {"size": 700, "mtime": 1747545501078, "results": "122", "hashOfConfig": "110"}, {"size": 14255, "mtime": 1749366744543, "results": "123", "hashOfConfig": "110"}, {"size": 3028, "mtime": 1748816305304, "results": "124", "hashOfConfig": "110"}, {"size": 1630, "mtime": 1746336079554, "results": "125", "hashOfConfig": "110"}, {"size": 1909, "mtime": 1748722592098, "results": "126", "hashOfConfig": "110"}, {"size": 64697, "mtime": 1749487794852, "results": "127", "hashOfConfig": "110"}, {"size": 324, "mtime": 1749417799670, "results": "128", "hashOfConfig": "110"}, {"size": 9068, "mtime": 1746856425683, "results": "129", "hashOfConfig": "110"}, {"size": 2210, "mtime": 1747432283057, "results": "130", "hashOfConfig": "110"}, {"size": 4494, "mtime": 1748121063631, "results": "131", "hashOfConfig": "110"}, {"size": 52535, "mtime": 1749414973931, "results": "132", "hashOfConfig": "110"}, {"size": 3337, "mtime": 1748816346924, "results": "133", "hashOfConfig": "110"}, {"size": 2958, "mtime": 1748816316425, "results": "134", "hashOfConfig": "110"}, {"size": 3507, "mtime": 1748816326922, "results": "135", "hashOfConfig": "110"}, {"size": 3340, "mtime": 1748816336281, "results": "136", "hashOfConfig": "110"}, {"size": 6125, "mtime": 1748705680231, "results": "137", "hashOfConfig": "110"}, {"size": 5880, "mtime": 1748121404574, "results": "138", "hashOfConfig": "110"}, {"size": 3889, "mtime": 1748664890350, "results": "139", "hashOfConfig": "110"}, {"size": 4720, "mtime": 1746771178920, "results": "140", "hashOfConfig": "110"}, {"size": 7681, "mtime": 1749184406942, "results": "141", "hashOfConfig": "110"}, {"size": 10819, "mtime": 1749184481438, "results": "142", "hashOfConfig": "110"}, {"size": 6259, "mtime": 1746965906057, "results": "143", "hashOfConfig": "110"}, {"size": 4215, "mtime": 1746278746358, "results": "144", "hashOfConfig": "110"}, {"size": 1273, "mtime": 1746809069006, "results": "145", "hashOfConfig": "110"}, {"size": 14270, "mtime": 1748371983481, "results": "146", "hashOfConfig": "110"}, {"size": 2752, "mtime": 1747022186740, "results": "147", "hashOfConfig": "110"}, {"size": 1072, "mtime": 1746637929350, "results": "148", "hashOfConfig": "110"}, {"size": 6745, "mtime": 1747545492454, "results": "149", "hashOfConfig": "110"}, {"size": 500, "mtime": 1748722841235, "results": "150", "hashOfConfig": "110"}, {"size": 43883, "mtime": 1749161040576, "results": "151", "hashOfConfig": "110"}, {"size": 1947, "mtime": 1748120984640, "results": "152", "hashOfConfig": "110"}, {"size": 53899, "mtime": 1749153422157, "results": "153", "hashOfConfig": "110"}, {"size": 13911, "mtime": 1749069212408, "results": "154", "hashOfConfig": "110"}, {"size": 18004, "mtime": 1749189211106, "results": "155", "hashOfConfig": "110"}, {"size": 11835, "mtime": 1748920731807, "results": "156", "hashOfConfig": "110"}, {"size": 2211, "mtime": 1748686293878, "results": "157", "hashOfConfig": "110"}, {"size": 9215, "mtime": 1749162481509, "results": "158", "hashOfConfig": "110"}, {"size": 10993, "mtime": 1747154871546, "results": "159", "hashOfConfig": "110"}, {"size": 12217, "mtime": 1749161883257, "results": "160", "hashOfConfig": "110"}, {"size": 20081, "mtime": 1749162690470, "results": "161", "hashOfConfig": "110"}, {"size": 7032, "mtime": 1748069273238, "results": "162", "hashOfConfig": "110"}, {"size": 8589, "mtime": 1748207111023, "results": "163", "hashOfConfig": "110"}, {"size": 13653, "mtime": 1749367215461, "results": "164", "hashOfConfig": "110"}, {"size": 12817, "mtime": 1749183241975, "results": "165", "hashOfConfig": "110"}, {"size": 36555, "mtime": 1747684003188, "results": "166", "hashOfConfig": "110"}, {"size": 9128, "mtime": 1749069292534, "results": "167", "hashOfConfig": "110"}, {"size": 20387, "mtime": 1748984521895, "results": "168", "hashOfConfig": "110"}, {"size": 522, "mtime": 1747022186711, "results": "169", "hashOfConfig": "110"}, {"size": 11907, "mtime": 1749189769410, "results": "170", "hashOfConfig": "110"}, {"size": 8399, "mtime": 1749489596923, "results": "171", "hashOfConfig": "110"}, {"size": 1703, "mtime": 1746972529152, "results": "172", "hashOfConfig": "110"}, {"size": 18402, "mtime": 1749156991134, "results": "173", "hashOfConfig": "110"}, {"size": 12050, "mtime": 1747547543421, "results": "174", "hashOfConfig": "110"}, {"size": 1686, "mtime": 1746946499500, "results": "175", "hashOfConfig": "110"}, {"size": 5145, "mtime": 1746914029633, "results": "176", "hashOfConfig": "110"}, {"size": 10253, "mtime": 1749156772006, "results": "177", "hashOfConfig": "110"}, {"size": 32582, "mtime": 1749161413266, "results": "178", "hashOfConfig": "110"}, {"size": 2574, "mtime": 1748920719208, "results": "179", "hashOfConfig": "110"}, {"size": 4094, "mtime": 1748161663641, "results": "180", "hashOfConfig": "110"}, {"size": 4717, "mtime": 1749142942884, "results": "181", "hashOfConfig": "110"}, {"size": 4346, "mtime": 1747491472989, "results": "182", "hashOfConfig": "110"}, {"size": 15647, "mtime": 1748899398456, "results": "183", "hashOfConfig": "110"}, {"size": 7659, "mtime": 1749366714525, "results": "184", "hashOfConfig": "110"}, {"size": 12341, "mtime": 1749366595552, "results": "185", "hashOfConfig": "110"}, {"size": 15764, "mtime": 1748877145346, "results": "186", "hashOfConfig": "110"}, {"size": 6899, "mtime": 1748877131332, "results": "187", "hashOfConfig": "110"}, {"size": 5536, "mtime": 1748670096009, "results": "188", "hashOfConfig": "110"}, {"size": 5457, "mtime": 1748666884369, "results": "189", "hashOfConfig": "110"}, {"size": 5605, "mtime": 1748666925194, "results": "190", "hashOfConfig": "110"}, {"size": 82038, "mtime": 1749413441723, "results": "191", "hashOfConfig": "110"}, {"size": 2844, "mtime": 1749490688188, "results": "192", "hashOfConfig": "110"}, {"size": 22794, "mtime": 1749490955320, "results": "193", "hashOfConfig": "110"}, {"size": 3708, "mtime": 1748705727900, "results": "194", "hashOfConfig": "110"}, {"size": 10270, "mtime": 1748724524628, "results": "195", "hashOfConfig": "110"}, {"size": 15055, "mtime": 1748755908778, "results": "196", "hashOfConfig": "110"}, {"size": 16415, "mtime": 1748755956687, "results": "197", "hashOfConfig": "110"}, {"size": 3434, "mtime": 1748755857115, "results": "198", "hashOfConfig": "110"}, {"size": 3483, "mtime": 1748755829302, "results": "199", "hashOfConfig": "110"}, {"size": 3508, "mtime": 1748755842942, "results": "200", "hashOfConfig": "110"}, {"size": 956, "mtime": 1748878396989, "results": "201", "hashOfConfig": "110"}, {"size": 17759, "mtime": 1749489767422, "results": "202", "hashOfConfig": "110"}, {"size": 16151, "mtime": 1748981113532, "results": "203", "hashOfConfig": "110"}, {"size": 3613, "mtime": 1748921268108, "results": "204", "hashOfConfig": "110"}, {"size": 1153, "mtime": 1748921279608, "results": "205", "hashOfConfig": "110"}, {"size": 6579, "mtime": 1748922219011, "results": "206", "hashOfConfig": "110"}, {"size": 8976, "mtime": 1748922249445, "results": "207", "hashOfConfig": "110"}, {"size": 29408, "mtime": 1749154958292, "results": "208", "hashOfConfig": "110"}, {"size": 24586, "mtime": 1749155015426, "results": "209", "hashOfConfig": "110"}, {"size": 11668, "mtime": 1749366480246, "results": "210", "hashOfConfig": "110"}, {"size": 3389, "mtime": 1749417624087, "results": "211", "hashOfConfig": "110"}, {"size": 15031, "mtime": 1749439918292, "results": "212", "hashOfConfig": "110"}, {"size": 15003, "mtime": 1749419448637, "results": "213", "hashOfConfig": "110"}, {"size": 14243, "mtime": 1749419412675, "results": "214", "hashOfConfig": "110"}, {"size": 4833, "mtime": 1749487592760, "results": "215", "hashOfConfig": "110"}, {"size": 14380, "mtime": 1749489582520, "results": "216", "hashOfConfig": "110"}, {"size": 22839, "mtime": 1749490722343, "results": "217", "hashOfConfig": "110"}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f0jzw9", {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\CMS\\webapp\\frontend\\src\\index.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\App.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js", ["542"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js", ["543", "544", "545", "546"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js", ["547", "548", "549", "550", "551", "552", "553", "554", "555", "556", "557", "558"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js", ["559"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js", ["560", "561", "562", "563", "564", "565", "566", "567", "568", "569"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js", ["570"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js", ["571", "572", "573", "574", "575", "576", "577", "578", "579", "580", "581", "582", "583", "584", "585"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js", ["586"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js", ["587", "588", "589", "590", "591", "592", "593", "594"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js", ["595", "596", "597", "598", "599", "600", "601", "602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622"], [], "C:\\CMS\\webapp\\frontend\\src\\config.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js", ["623"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx", ["624"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js", ["625"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js", ["626", "627", "628", "629"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js", ["630", "631"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js", ["632", "633"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js", ["634", "635", "636", "637"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js", ["638", "639"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js", ["640", "641"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js", ["642", "643"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js", ["644", "645"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js", ["646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661", "662", "663", "664", "665"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js", ["666", "667", "668", "669", "670", "671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js", ["688", "689", "690", "691"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js", ["692", "693", "694", "695", "696", "697", "698", "699", "700"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js", ["701", "702"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js", ["703", "704", "705", "706", "707", "708", "709", "710", "711", "712", "713"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js", ["714"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js", ["715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725", "726", "727", "728", "729", "730", "731", "732", "733", "734", "735", "736"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx", ["737"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx", ["738"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx", ["739"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js", ["740", "741", "742", "743", "744", "745", "746", "747", "748", "749", "750"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js", ["751", "752", "753", "754"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js", ["755", "756", "757", "758", "759", "760", "761", "762", "763", "764", "765", "766", "767", "768"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js", ["769", "770"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js", ["771"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js", ["772", "773"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js", ["774", "775", "776", "777", "778", "779", "780", "781", "782", "783", "784", "785", "786", "787", "788", "789", "790"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js", ["791", "792", "793", "794", "795"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js", ["796"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js", ["797", "798", "799"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js", ["800", "801"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js", ["802", "803"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js", ["804"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js", ["805"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js", ["806"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js", ["807", "808", "809", "810", "811", "812"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\comande\\ComandePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeList.js", ["813", "814"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\TestComande.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\AccessoRapidoComanda.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\RapportiGenerali.js", ["815", "816", "817", "818", "819", "820", "821", "822", "823"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\ProveDettagliate.js", ["824", "825", "826", "827", "828", "829", "830", "831", "832"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\nonConformitaService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\rapportiGeneraliService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\proveDettagliateService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\Logo.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaConCavi.js", ["833", "834", "835", "836", "837", "838"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SmartCaviFilter.js", ["839", "840", "841"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ContextMenu.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\hooks\\useContextMenu.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialog.js", ["842", "843", "844", "845"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialogCompleto.js", ["846", "847", "848", "849"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialogCompleto.js", ["850", "851", "852", "853", "854", "855", "856", "857", "858", "859", "860", "861", "862", "863", "864", "865", "866", "867", "868", "869", "870", "871", "872", "873"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CreateCantiereDialog.js", ["874"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\weatherService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaMultipla.js", ["875", "876", "877", "878", "879"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ValidationResultsDialog.js", ["880"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\comandeValidationService.js", ["881", "882", "883", "884", "885"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\responsabiliService.js", ["886", "887", "888"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\responsabili\\GestioneResponsabili.js", ["889", "890", "891", "892", "893", "894", "895", "896", "897", "898", "899"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeListRivoluzionato.js", ["900"], [], {"ruleId": "901", "severity": 1, "message": "902", "line": 12, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 12, "endColumn": 14}, {"ruleId": "905", "severity": 1, "message": "906", "line": 78, "column": 11, "nodeType": "907", "messageId": "908", "endLine": 78, "endColumn": 115}, {"ruleId": "905", "severity": 1, "message": "906", "line": 80, "column": 11, "nodeType": "907", "messageId": "908", "endLine": 80, "endColumn": 107}, {"ruleId": "905", "severity": 1, "message": "906", "line": 86, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 86, "endColumn": 105}, {"ruleId": "905", "severity": 1, "message": "906", "line": 89, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 89, "endColumn": 41}, {"ruleId": "901", "severity": 1, "message": "909", "line": 13, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 13, "endColumn": 9}, {"ruleId": "901", "severity": 1, "message": "910", "line": 20, "column": 25, "nodeType": "903", "messageId": "904", "endLine": 20, "endColumn": 34}, {"ruleId": "901", "severity": 1, "message": "911", "line": 21, "column": 19, "nodeType": "903", "messageId": "904", "endLine": 21, "endColumn": 35}, {"ruleId": "901", "severity": 1, "message": "912", "line": 22, "column": 12, "nodeType": "903", "messageId": "904", "endLine": 22, "endColumn": 21}, {"ruleId": "901", "severity": 1, "message": "913", "line": 23, "column": 18, "nodeType": "903", "messageId": "904", "endLine": 23, "endColumn": 28}, {"ruleId": "901", "severity": 1, "message": "914", "line": 40, "column": 11, "nodeType": "903", "messageId": "904", "endLine": 40, "endColumn": 35}, {"ruleId": "901", "severity": 1, "message": "915", "line": 40, "column": 37, "nodeType": "903", "messageId": "904", "endLine": 40, "endColumn": 62}, {"ruleId": "901", "severity": 1, "message": "916", "line": 57, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 57, "endColumn": 22}, {"ruleId": "901", "severity": 1, "message": "917", "line": 58, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 58, "endColumn": 23}, {"ruleId": "901", "severity": 1, "message": "918", "line": 59, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 59, "endColumn": 26}, {"ruleId": "901", "severity": 1, "message": "919", "line": 60, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 60, "endColumn": 22}, {"ruleId": "901", "severity": 1, "message": "920", "line": 69, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 69, "endColumn": 29}, {"ruleId": "901", "severity": 1, "message": "921", "line": 1, "column": 8, "nodeType": "903", "messageId": "904", "endLine": 1, "endColumn": 13}, {"ruleId": "901", "severity": 1, "message": "922", "line": 2, "column": 27, "nodeType": "903", "messageId": "904", "endLine": 2, "endColumn": 31}, {"ruleId": "901", "severity": 1, "message": "923", "line": 2, "column": 33, "nodeType": "903", "messageId": "904", "endLine": 2, "endColumn": 37}, {"ruleId": "901", "severity": 1, "message": "924", "line": 2, "column": 39, "nodeType": "903", "messageId": "904", "endLine": 2, "endColumn": 50}, {"ruleId": "901", "severity": 1, "message": "925", "line": 2, "column": 52, "nodeType": "903", "messageId": "904", "endLine": 2, "endColumn": 66}, {"ruleId": "901", "severity": 1, "message": "909", "line": 2, "column": 68, "nodeType": "903", "messageId": "904", "endLine": 2, "endColumn": 74}, {"ruleId": "901", "severity": 1, "message": "910", "line": 5, "column": 25, "nodeType": "903", "messageId": "904", "endLine": 5, "endColumn": 34}, {"ruleId": "901", "severity": 1, "message": "911", "line": 6, "column": 19, "nodeType": "903", "messageId": "904", "endLine": 6, "endColumn": 35}, {"ruleId": "901", "severity": 1, "message": "912", "line": 7, "column": 12, "nodeType": "903", "messageId": "904", "endLine": 7, "endColumn": 21}, {"ruleId": "901", "severity": 1, "message": "913", "line": 8, "column": 18, "nodeType": "903", "messageId": "904", "endLine": 8, "endColumn": 28}, {"ruleId": "901", "severity": 1, "message": "926", "line": 43, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 43, "endColumn": 19}, {"ruleId": "901", "severity": 1, "message": "927", "line": 16, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 16, "endColumn": 12}, {"ruleId": "901", "severity": 1, "message": "923", "line": 8, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 8, "endColumn": 7}, {"ruleId": "901", "severity": 1, "message": "924", "line": 9, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 9, "endColumn": 14}, {"ruleId": "901", "severity": 1, "message": "902", "line": 10, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 10, "endColumn": 14}, {"ruleId": "901", "severity": 1, "message": "922", "line": 11, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 11, "endColumn": 7}, {"ruleId": "901", "severity": 1, "message": "928", "line": 12, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 12, "endColumn": 10}, {"ruleId": "901", "severity": 1, "message": "929", "line": 15, "column": 11, "nodeType": "903", "messageId": "904", "endLine": 15, "endColumn": 19}, {"ruleId": "901", "severity": 1, "message": "930", "line": 16, "column": 15, "nodeType": "903", "messageId": "904", "endLine": 16, "endColumn": 27}, {"ruleId": "901", "severity": 1, "message": "931", "line": 17, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 17, "endColumn": 17}, {"ruleId": "901", "severity": 1, "message": "932", "line": 18, "column": 11, "nodeType": "903", "messageId": "904", "endLine": 18, "endColumn": 19}, {"ruleId": "901", "severity": 1, "message": "933", "line": 19, "column": 13, "nodeType": "903", "messageId": "904", "endLine": 19, "endColumn": 23}, {"ruleId": "901", "severity": 1, "message": "934", "line": 20, "column": 14, "nodeType": "903", "messageId": "904", "endLine": 20, "endColumn": 25}, {"ruleId": "901", "severity": 1, "message": "935", "line": 25, "column": 8, "nodeType": "903", "messageId": "904", "endLine": 25, "endColumn": 17}, {"ruleId": "901", "severity": 1, "message": "936", "line": 28, "column": 11, "nodeType": "903", "messageId": "904", "endLine": 28, "endColumn": 26}, {"ruleId": "901", "severity": 1, "message": "937", "line": 48, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 48, "endColumn": 22}, {"ruleId": "901", "severity": 1, "message": "938", "line": 53, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 53, "endColumn": 20}, {"ruleId": "901", "severity": 1, "message": "939", "line": 11, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 11, "endColumn": 19}, {"ruleId": "901", "severity": 1, "message": "940", "line": 4, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 4, "endColumn": 13}, {"ruleId": "901", "severity": 1, "message": "941", "line": 5, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 5, "endColumn": 8}, {"ruleId": "901", "severity": 1, "message": "942", "line": 7, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 7, "endColumn": 13}, {"ruleId": "901", "severity": 1, "message": "943", "line": 12, "column": 14, "nodeType": "903", "messageId": "904", "endLine": 12, "endColumn": 25}, {"ruleId": "901", "severity": 1, "message": "929", "line": 13, "column": 11, "nodeType": "903", "messageId": "904", "endLine": 13, "endColumn": 19}, {"ruleId": "901", "severity": 1, "message": "944", "line": 17, "column": 8, "nodeType": "903", "messageId": "904", "endLine": 17, "endColumn": 23}, {"ruleId": "901", "severity": 1, "message": "936", "line": 21, "column": 11, "nodeType": "903", "messageId": "904", "endLine": 21, "endColumn": 26}, {"ruleId": "901", "severity": 1, "message": "945", "line": 26, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 26, "endColumn": 21}, {"ruleId": "901", "severity": 1, "message": "923", "line": 8, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 8, "endColumn": 7}, {"ruleId": "901", "severity": 1, "message": "924", "line": 9, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 9, "endColumn": 14}, {"ruleId": "901", "severity": 1, "message": "942", "line": 11, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 11, "endColumn": 13}, {"ruleId": "901", "severity": 1, "message": "946", "line": 14, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 14, "endColumn": 17}, {"ruleId": "901", "severity": 1, "message": "947", "line": 26, "column": 8, "nodeType": "903", "messageId": "904", "endLine": 26, "endColumn": 16}, {"ruleId": "901", "severity": 1, "message": "948", "line": 30, "column": 15, "nodeType": "903", "messageId": "904", "endLine": 30, "endColumn": 27}, {"ruleId": "901", "severity": 1, "message": "949", "line": 32, "column": 14, "nodeType": "903", "messageId": "904", "endLine": 32, "endColumn": 25}, {"ruleId": "901", "severity": 1, "message": "950", "line": 33, "column": 15, "nodeType": "903", "messageId": "904", "endLine": 33, "endColumn": 27}, {"ruleId": "901", "severity": 1, "message": "951", "line": 34, "column": 15, "nodeType": "903", "messageId": "904", "endLine": 34, "endColumn": 27}, {"ruleId": "901", "severity": 1, "message": "952", "line": 35, "column": 27, "nodeType": "903", "messageId": "904", "endLine": 35, "endColumn": 51}, {"ruleId": "901", "severity": 1, "message": "953", "line": 42, "column": 15, "nodeType": "903", "messageId": "904", "endLine": 42, "endColumn": 27}, {"ruleId": "901", "severity": 1, "message": "954", "line": 51, "column": 8, "nodeType": "903", "messageId": "904", "endLine": 51, "endColumn": 24}, {"ruleId": "901", "severity": 1, "message": "955", "line": 52, "column": 8, "nodeType": "903", "messageId": "904", "endLine": 52, "endColumn": 16}, {"ruleId": "901", "severity": 1, "message": "936", "line": 65, "column": 11, "nodeType": "903", "messageId": "904", "endLine": 65, "endColumn": 26}, {"ruleId": "901", "severity": 1, "message": "956", "line": 66, "column": 11, "nodeType": "903", "messageId": "904", "endLine": 66, "endColumn": 32}, {"ruleId": "901", "severity": 1, "message": "914", "line": 66, "column": 34, "nodeType": "903", "messageId": "904", "endLine": 66, "endColumn": 58}, {"ruleId": "901", "severity": 1, "message": "957", "line": 66, "column": 60, "nodeType": "903", "messageId": "904", "endLine": 66, "endColumn": 82}, {"ruleId": "901", "severity": 1, "message": "915", "line": 66, "column": 84, "nodeType": "903", "messageId": "904", "endLine": 66, "endColumn": 109}, {"ruleId": "901", "severity": 1, "message": "958", "line": 66, "column": 111, "nodeType": "903", "messageId": "904", "endLine": 66, "endColumn": 133}, {"ruleId": "901", "severity": 1, "message": "959", "line": 66, "column": 135, "nodeType": "903", "messageId": "904", "endLine": 66, "endColumn": 160}, {"ruleId": "901", "severity": 1, "message": "960", "line": 67, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 67, "endColumn": 17}, {"ruleId": "901", "severity": 1, "message": "945", "line": 69, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 69, "endColumn": 22}, {"ruleId": "901", "severity": 1, "message": "961", "line": 312, "column": 19, "nodeType": "903", "messageId": "904", "endLine": 312, "endColumn": 29}, {"ruleId": "901", "severity": 1, "message": "962", "line": 320, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 320, "endColumn": 28}, {"ruleId": "901", "severity": 1, "message": "963", "line": 321, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 321, "endColumn": 23}, {"ruleId": "901", "severity": 1, "message": "964", "line": 321, "column": 25, "nodeType": "903", "messageId": "904", "endLine": 321, "endColumn": 41}, {"ruleId": "965", "severity": 1, "message": "966", "line": 681, "column": 6, "nodeType": "967", "endLine": 681, "endColumn": 15, "suggestions": "968"}, {"ruleId": "901", "severity": 1, "message": "969", "line": 879, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 879, "endColumn": 33}, {"ruleId": "901", "severity": 1, "message": "970", "line": 1, "column": 27, "nodeType": "903", "messageId": "904", "endLine": 1, "endColumn": 36}, {"ruleId": "901", "severity": 1, "message": "971", "line": 49, "column": 19, "nodeType": "903", "messageId": "904", "endLine": 49, "endColumn": 26}, {"ruleId": "965", "severity": 1, "message": "972", "line": 178, "column": 6, "nodeType": "967", "endLine": 178, "endColumn": 38, "suggestions": "973"}, {"ruleId": "901", "severity": 1, "message": "940", "line": 4, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 4, "endColumn": 13}, {"ruleId": "901", "severity": 1, "message": "941", "line": 5, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 5, "endColumn": 8}, {"ruleId": "901", "severity": 1, "message": "945", "line": 26, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 26, "endColumn": 21}, {"ruleId": "901", "severity": 1, "message": "974", "line": 48, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 48, "endColumn": 29}, {"ruleId": "901", "severity": 1, "message": "940", "line": 4, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 4, "endColumn": 13}, {"ruleId": "901", "severity": 1, "message": "974", "line": 37, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 37, "endColumn": 29}, {"ruleId": "901", "severity": 1, "message": "940", "line": 4, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 4, "endColumn": 13}, {"ruleId": "901", "severity": 1, "message": "974", "line": 52, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 52, "endColumn": 29}, {"ruleId": "901", "severity": 1, "message": "940", "line": 4, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 4, "endColumn": 13}, {"ruleId": "901", "severity": 1, "message": "941", "line": 5, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 5, "endColumn": 8}, {"ruleId": "901", "severity": 1, "message": "945", "line": 26, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 26, "endColumn": 21}, {"ruleId": "901", "severity": 1, "message": "974", "line": 48, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 48, "endColumn": 29}, {"ruleId": "901", "severity": 1, "message": "936", "line": 24, "column": 11, "nodeType": "903", "messageId": "904", "endLine": 24, "endColumn": 26}, {"ruleId": "965", "severity": 1, "message": "975", "line": 53, "column": 6, "nodeType": "967", "endLine": 53, "endColumn": 18, "suggestions": "976"}, {"ruleId": "901", "severity": 1, "message": "977", "line": 1, "column": 8, "nodeType": "903", "messageId": "904", "endLine": 1, "endColumn": 13}, {"ruleId": "901", "severity": 1, "message": "978", "line": 5, "column": 7, "nodeType": "903", "messageId": "904", "endLine": 5, "endColumn": 14}, {"ruleId": "901", "severity": 1, "message": "928", "line": 14, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 14, "endColumn": 10}, {"ruleId": "901", "severity": 1, "message": "979", "line": 28, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 28, "endColumn": 18}, {"ruleId": "901", "severity": 1, "message": "977", "line": 1, "column": 8, "nodeType": "903", "messageId": "904", "endLine": 1, "endColumn": 13}, {"ruleId": "901", "severity": 1, "message": "978", "line": 5, "column": 7, "nodeType": "903", "messageId": "904", "endLine": 5, "endColumn": 14}, {"ruleId": "901", "severity": 1, "message": "923", "line": 8, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 8, "endColumn": 7}, {"ruleId": "901", "severity": 1, "message": "924", "line": 9, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 9, "endColumn": 14}, {"ruleId": "901", "severity": 1, "message": "902", "line": 10, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 10, "endColumn": 14}, {"ruleId": "901", "severity": 1, "message": "980", "line": 23, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 23, "endColumn": 15}, {"ruleId": "901", "severity": 1, "message": "981", "line": 24, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 24, "endColumn": 17}, {"ruleId": "901", "severity": 1, "message": "928", "line": 25, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 25, "endColumn": 10}, {"ruleId": "901", "severity": 1, "message": "942", "line": 29, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 29, "endColumn": 13}, {"ruleId": "901", "severity": 1, "message": "982", "line": 30, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 30, "endColumn": 8}, {"ruleId": "901", "severity": 1, "message": "983", "line": 31, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 31, "endColumn": 12}, {"ruleId": "901", "severity": 1, "message": "984", "line": 32, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 32, "endColumn": 12}, {"ruleId": "901", "severity": 1, "message": "985", "line": 33, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 33, "endColumn": 17}, {"ruleId": "901", "severity": 1, "message": "986", "line": 34, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 34, "endColumn": 12}, {"ruleId": "901", "severity": 1, "message": "987", "line": 35, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 35, "endColumn": 11}, {"ruleId": "901", "severity": 1, "message": "932", "line": 39, "column": 11, "nodeType": "903", "messageId": "904", "endLine": 39, "endColumn": 19}, {"ruleId": "901", "severity": 1, "message": "930", "line": 43, "column": 15, "nodeType": "903", "messageId": "904", "endLine": 43, "endColumn": 27}, {"ruleId": "901", "severity": 1, "message": "988", "line": 44, "column": 14, "nodeType": "903", "messageId": "904", "endLine": 44, "endColumn": 25}, {"ruleId": "901", "severity": 1, "message": "989", "line": 50, "column": 69, "nodeType": "903", "messageId": "904", "endLine": 50, "endColumn": 76}, {"ruleId": "901", "severity": 1, "message": "990", "line": 79, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 79, "endColumn": 26}, {"ruleId": "965", "severity": 1, "message": "991", "line": 161, "column": 6, "nodeType": "967", "endLine": 161, "endColumn": 8, "suggestions": "992"}, {"ruleId": "901", "severity": 1, "message": "993", "line": 675, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 675, "endColumn": 26}, {"ruleId": "905", "severity": 1, "message": "906", "line": 260, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 264, "endColumn": 11}, {"ruleId": "905", "severity": 1, "message": "906", "line": 274, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 274, "endColumn": 70}, {"ruleId": "905", "severity": 1, "message": "906", "line": 278, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 278, "endColumn": 54}, {"ruleId": "905", "severity": 1, "message": "906", "line": 333, "column": 11, "nodeType": "907", "messageId": "908", "endLine": 338, "endColumn": 13}, {"ruleId": "905", "severity": 1, "message": "906", "line": 435, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 439, "endColumn": 11}, {"ruleId": "905", "severity": 1, "message": "906", "line": 451, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 451, "endColumn": 54}, {"ruleId": "905", "severity": 1, "message": "906", "line": 668, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 668, "endColumn": 163}, {"ruleId": "905", "severity": 1, "message": "906", "line": 677, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 677, "endColumn": 70}, {"ruleId": "905", "severity": 1, "message": "906", "line": 681, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 681, "endColumn": 54}, {"ruleId": "901", "severity": 1, "message": "994", "line": 755, "column": 17, "nodeType": "903", "messageId": "904", "endLine": 755, "endColumn": 22}, {"ruleId": "905", "severity": 1, "message": "906", "line": 775, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 779, "endColumn": 11}, {"ruleId": "905", "severity": 1, "message": "906", "line": 794, "column": 11, "nodeType": "907", "messageId": "908", "endLine": 798, "endColumn": 13}, {"ruleId": "905", "severity": 1, "message": "906", "line": 801, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 804, "endColumn": 11}, {"ruleId": "905", "severity": 1, "message": "906", "line": 810, "column": 11, "nodeType": "907", "messageId": "908", "endLine": 814, "endColumn": 13}, {"ruleId": "905", "severity": 1, "message": "906", "line": 817, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 820, "endColumn": 11}, {"ruleId": "905", "severity": 1, "message": "906", "line": 885, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 889, "endColumn": 11}, {"ruleId": "995", "severity": 1, "message": "996", "line": 955, "column": 3, "nodeType": "997", "messageId": "998", "endLine": 955, "endColumn": 29}, {"ruleId": "995", "severity": 1, "message": "999", "line": 1143, "column": 3, "nodeType": "997", "messageId": "998", "endLine": 1143, "endColumn": 23}, {"ruleId": "905", "severity": 1, "message": "906", "line": 1252, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 1252, "endColumn": 163}, {"ruleId": "905", "severity": 1, "message": "906", "line": 1282, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 1282, "endColumn": 163}, {"ruleId": "905", "severity": 1, "message": "906", "line": 1335, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 1335, "endColumn": 163}, {"ruleId": "905", "severity": 1, "message": "906", "line": 1382, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 1382, "endColumn": 163}, {"ruleId": "901", "severity": 1, "message": "1000", "line": 6, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 6, "endColumn": 8}, {"ruleId": "901", "severity": 1, "message": "928", "line": 11, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 11, "endColumn": 10}, {"ruleId": "901", "severity": 1, "message": "1001", "line": 20, "column": 13, "nodeType": "903", "messageId": "904", "endLine": 20, "endColumn": 23}, {"ruleId": "901", "severity": 1, "message": "1002", "line": 205, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 205, "endColumn": 21}, {"ruleId": "901", "severity": 1, "message": "940", "line": 2, "column": 15, "nodeType": "903", "messageId": "904", "endLine": 2, "endColumn": 25}, {"ruleId": "901", "severity": 1, "message": "1003", "line": 2, "column": 64, "nodeType": "903", "messageId": "904", "endLine": 2, "endColumn": 70}, {"ruleId": "901", "severity": 1, "message": "951", "line": 4, "column": 15, "nodeType": "903", "messageId": "904", "endLine": 4, "endColumn": 27}, {"ruleId": "901", "severity": 1, "message": "1004", "line": 5, "column": 12, "nodeType": "903", "messageId": "904", "endLine": 5, "endColumn": 21}, {"ruleId": "901", "severity": 1, "message": "1005", "line": 6, "column": 17, "nodeType": "903", "messageId": "904", "endLine": 6, "endColumn": 26}, {"ruleId": "901", "severity": 1, "message": "953", "line": 7, "column": 15, "nodeType": "903", "messageId": "904", "endLine": 7, "endColumn": 27}, {"ruleId": "901", "severity": 1, "message": "1006", "line": 8, "column": 16, "nodeType": "903", "messageId": "904", "endLine": 8, "endColumn": 25}, {"ruleId": "901", "severity": 1, "message": "1007", "line": 14, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 14, "endColumn": 20}, {"ruleId": "901", "severity": 1, "message": "1008", "line": 121, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 121, "endColumn": 29}, {"ruleId": "901", "severity": 1, "message": "977", "line": 1, "column": 8, "nodeType": "903", "messageId": "904", "endLine": 1, "endColumn": 13}, {"ruleId": "901", "severity": 1, "message": "978", "line": 5, "column": 7, "nodeType": "903", "messageId": "904", "endLine": 5, "endColumn": 14}, {"ruleId": "901", "severity": 1, "message": "1009", "line": 3, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 3, "endColumn": 11}, {"ruleId": "901", "severity": 1, "message": "1010", "line": 4, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 4, "endColumn": 6}, {"ruleId": "901", "severity": 1, "message": "1011", "line": 5, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 5, "endColumn": 7}, {"ruleId": "901", "severity": 1, "message": "1012", "line": 6, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 6, "endColumn": 11}, {"ruleId": "901", "severity": 1, "message": "1013", "line": 7, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 7, "endColumn": 6}, {"ruleId": "901", "severity": 1, "message": "1014", "line": 12, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 12, "endColumn": 9}, {"ruleId": "901", "severity": 1, "message": "1015", "line": 36, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 36, "endColumn": 21}, {"ruleId": "901", "severity": 1, "message": "1016", "line": 50, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 50, "endColumn": 17}, {"ruleId": "901", "severity": 1, "message": "1017", "line": 64, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 64, "endColumn": 20}, {"ruleId": "901", "severity": 1, "message": "1018", "line": 88, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 88, "endColumn": 22}, {"ruleId": "901", "severity": 1, "message": "1019", "line": 104, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 104, "endColumn": 30}, {"ruleId": "901", "severity": 1, "message": "1020", "line": 3, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 3, "endColumn": 12}, {"ruleId": "901", "severity": 1, "message": "1012", "line": 3, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 3, "endColumn": 11}, {"ruleId": "901", "severity": 1, "message": "1013", "line": 4, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 4, "endColumn": 6}, {"ruleId": "901", "severity": 1, "message": "1021", "line": 5, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 5, "endColumn": 8}, {"ruleId": "901", "severity": 1, "message": "1022", "line": 6, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 6, "endColumn": 8}, {"ruleId": "901", "severity": 1, "message": "1023", "line": 7, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 7, "endColumn": 16}, {"ruleId": "901", "severity": 1, "message": "1024", "line": 8, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 8, "endColumn": 10}, {"ruleId": "901", "severity": 1, "message": "1014", "line": 9, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 9, "endColumn": 9}, {"ruleId": "901", "severity": 1, "message": "1025", "line": 10, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 10, "endColumn": 22}, {"ruleId": "901", "severity": 1, "message": "1009", "line": 11, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 11, "endColumn": 11}, {"ruleId": "901", "severity": 1, "message": "1010", "line": 12, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 12, "endColumn": 6}, {"ruleId": "901", "severity": 1, "message": "1011", "line": 13, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 13, "endColumn": 7}, {"ruleId": "901", "severity": 1, "message": "1026", "line": 14, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 14, "endColumn": 16}, {"ruleId": "901", "severity": 1, "message": "1027", "line": 15, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 15, "endColumn": 7}, {"ruleId": "901", "severity": 1, "message": "1020", "line": 16, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 16, "endColumn": 12}, {"ruleId": "901", "severity": 1, "message": "1028", "line": 18, "column": 40, "nodeType": "903", "messageId": "904", "endLine": 18, "endColumn": 44}, {"ruleId": "901", "severity": 1, "message": "1029", "line": 47, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 47, "endColumn": 19}, {"ruleId": "901", "severity": 1, "message": "1030", "line": 64, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 64, "endColumn": 19}, {"ruleId": "901", "severity": 1, "message": "1031", "line": 71, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 71, "endColumn": 20}, {"ruleId": "901", "severity": 1, "message": "1018", "line": 79, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 79, "endColumn": 22}, {"ruleId": "901", "severity": 1, "message": "1019", "line": 95, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 95, "endColumn": 30}, {"ruleId": "901", "severity": 1, "message": "1032", "line": 272, "column": 27, "nodeType": "903", "messageId": "904", "endLine": 272, "endColumn": 37}, {"ruleId": "901", "severity": 1, "message": "1033", "line": 273, "column": 27, "nodeType": "903", "messageId": "904", "endLine": 273, "endColumn": 36}, {"ruleId": "901", "severity": 1, "message": "941", "line": 3, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 3, "endColumn": 8}, {"ruleId": "965", "severity": 1, "message": "1034", "line": 60, "column": 6, "nodeType": "967", "endLine": 60, "endColumn": 34, "suggestions": "1035"}, {"ruleId": "901", "severity": 1, "message": "1036", "line": 25, "column": 13, "nodeType": "903", "messageId": "904", "endLine": 25, "endColumn": 25}, {"ruleId": "901", "severity": 1, "message": "1037", "line": 33, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 33, "endColumn": 15}, {"ruleId": "901", "severity": 1, "message": "1038", "line": 34, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 34, "endColumn": 14}, {"ruleId": "901", "severity": 1, "message": "1039", "line": 35, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 35, "endColumn": 22}, {"ruleId": "901", "severity": 1, "message": "1040", "line": 36, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 36, "endColumn": 21}, {"ruleId": "901", "severity": 1, "message": "1041", "line": 37, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 37, "endColumn": 17}, {"ruleId": "901", "severity": 1, "message": "1042", "line": 41, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 41, "endColumn": 20}, {"ruleId": "901", "severity": 1, "message": "1043", "line": 43, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 43, "endColumn": 34}, {"ruleId": "901", "severity": 1, "message": "1044", "line": 69, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 69, "endColumn": 17}, {"ruleId": "901", "severity": 1, "message": "1045", "line": 69, "column": 19, "nodeType": "903", "messageId": "904", "endLine": 69, "endColumn": 29}, {"ruleId": "965", "severity": 1, "message": "1046", "line": 88, "column": 6, "nodeType": "967", "endLine": 88, "endColumn": 18, "suggestions": "1047"}, {"ruleId": "965", "severity": 1, "message": "1048", "line": 448, "column": 6, "nodeType": "967", "endLine": 448, "endColumn": 28, "suggestions": "1049"}, {"ruleId": "901", "severity": 1, "message": "927", "line": 4, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 4, "endColumn": 12}, {"ruleId": "901", "severity": 1, "message": "1001", "line": 21, "column": 20, "nodeType": "903", "messageId": "904", "endLine": 21, "endColumn": 30}, {"ruleId": "901", "severity": 1, "message": "1002", "line": 100, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 100, "endColumn": 21}, {"ruleId": "901", "severity": 1, "message": "1050", "line": 119, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 119, "endColumn": 30}, {"ruleId": "901", "severity": 1, "message": "1051", "line": 8, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 8, "endColumn": 7}, {"ruleId": "901", "severity": 1, "message": "1052", "line": 9, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 9, "endColumn": 11}, {"ruleId": "901", "severity": 1, "message": "1053", "line": 10, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 10, "endColumn": 15}, {"ruleId": "901", "severity": 1, "message": "1054", "line": 12, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 12, "endColumn": 9}, {"ruleId": "901", "severity": 1, "message": "1055", "line": 13, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 13, "endColumn": 14}, {"ruleId": "901", "severity": 1, "message": "1056", "line": 14, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 14, "endColumn": 16}, {"ruleId": "901", "severity": 1, "message": "1057", "line": 15, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 15, "endColumn": 16}, {"ruleId": "901", "severity": 1, "message": "1058", "line": 36, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 36, "endColumn": 30}, {"ruleId": "901", "severity": 1, "message": "1059", "line": 37, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 37, "endColumn": 20}, {"ruleId": "965", "severity": 1, "message": "1060", "line": 46, "column": 6, "nodeType": "967", "endLine": 46, "endColumn": 18, "suggestions": "1061"}, {"ruleId": "901", "severity": 1, "message": "1062", "line": 265, "column": 23, "nodeType": "903", "messageId": "904", "endLine": 265, "endColumn": 44}, {"ruleId": "901", "severity": 1, "message": "1063", "line": 266, "column": 23, "nodeType": "903", "messageId": "904", "endLine": 266, "endColumn": 42}, {"ruleId": "901", "severity": 1, "message": "1062", "line": 381, "column": 21, "nodeType": "903", "messageId": "904", "endLine": 381, "endColumn": 42}, {"ruleId": "901", "severity": 1, "message": "1063", "line": 382, "column": 21, "nodeType": "903", "messageId": "904", "endLine": 382, "endColumn": 40}, {"ruleId": "901", "severity": 1, "message": "977", "line": 1, "column": 8, "nodeType": "903", "messageId": "904", "endLine": 1, "endColumn": 13}, {"ruleId": "901", "severity": 1, "message": "978", "line": 5, "column": 7, "nodeType": "903", "messageId": "904", "endLine": 5, "endColumn": 14}, {"ruleId": "901", "severity": 1, "message": "1064", "line": 1, "column": 8, "nodeType": "903", "messageId": "904", "endLine": 1, "endColumn": 14}, {"ruleId": "901", "severity": 1, "message": "977", "line": 1, "column": 8, "nodeType": "903", "messageId": "904", "endLine": 1, "endColumn": 13}, {"ruleId": "901", "severity": 1, "message": "978", "line": 5, "column": 7, "nodeType": "903", "messageId": "904", "endLine": 5, "endColumn": 14}, {"ruleId": "901", "severity": 1, "message": "977", "line": 1, "column": 8, "nodeType": "903", "messageId": "904", "endLine": 1, "endColumn": 13}, {"ruleId": "901", "severity": 1, "message": "978", "line": 5, "column": 7, "nodeType": "903", "messageId": "904", "endLine": 5, "endColumn": 14}, {"ruleId": "901", "severity": 1, "message": "1065", "line": 83, "column": 13, "nodeType": "903", "messageId": "904", "endLine": 83, "endColumn": 21}, {"ruleId": "905", "severity": 1, "message": "906", "line": 109, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 109, "endColumn": 163}, {"ruleId": "905", "severity": 1, "message": "906", "line": 123, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 123, "endColumn": 70}, {"ruleId": "905", "severity": 1, "message": "906", "line": 127, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 127, "endColumn": 54}, {"ruleId": "905", "severity": 1, "message": "906", "line": 212, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 212, "endColumn": 163}, {"ruleId": "905", "severity": 1, "message": "906", "line": 226, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 226, "endColumn": 70}, {"ruleId": "905", "severity": 1, "message": "906", "line": 230, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 230, "endColumn": 54}, {"ruleId": "905", "severity": 1, "message": "906", "line": 271, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 271, "endColumn": 163}, {"ruleId": "905", "severity": 1, "message": "906", "line": 280, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 280, "endColumn": 70}, {"ruleId": "905", "severity": 1, "message": "906", "line": 284, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 284, "endColumn": 54}, {"ruleId": "905", "severity": 1, "message": "906", "line": 320, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 320, "endColumn": 70}, {"ruleId": "905", "severity": 1, "message": "906", "line": 324, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 324, "endColumn": 54}, {"ruleId": "905", "severity": 1, "message": "906", "line": 416, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 416, "endColumn": 163}, {"ruleId": "905", "severity": 1, "message": "906", "line": 425, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 425, "endColumn": 70}, {"ruleId": "905", "severity": 1, "message": "906", "line": 429, "column": 9, "nodeType": "907", "messageId": "908", "endLine": 429, "endColumn": 54}, {"ruleId": "901", "severity": 1, "message": "1044", "line": 60, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 60, "endColumn": 17}, {"ruleId": "901", "severity": 1, "message": "1045", "line": 60, "column": 19, "nodeType": "903", "messageId": "904", "endLine": 60, "endColumn": 29}, {"ruleId": "965", "severity": 1, "message": "1060", "line": 90, "column": 6, "nodeType": "967", "endLine": 90, "endColumn": 32, "suggestions": "1066"}, {"ruleId": "901", "severity": 1, "message": "1067", "line": 370, "column": 17, "nodeType": "903", "messageId": "904", "endLine": 370, "endColumn": 23}, {"ruleId": "901", "severity": 1, "message": "1068", "line": 470, "column": 17, "nodeType": "903", "messageId": "904", "endLine": 470, "endColumn": 25}, {"ruleId": "901", "severity": 1, "message": "1069", "line": 17, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 17, "endColumn": 8}, {"ruleId": "901", "severity": 1, "message": "1070", "line": 16, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 16, "endColumn": 11}, {"ruleId": "901", "severity": 1, "message": "1071", "line": 17, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 17, "endColumn": 9}, {"ruleId": "901", "severity": 1, "message": "1072", "line": 19, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 19, "endColumn": 13}, {"ruleId": "901", "severity": 1, "message": "947", "line": 14, "column": 11, "nodeType": "903", "messageId": "904", "endLine": 14, "endColumn": 19}, {"ruleId": "901", "severity": 1, "message": "1073", "line": 43, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 43, "endColumn": 26}, {"ruleId": "901", "severity": 1, "message": "942", "line": 12, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 12, "endColumn": 13}, {"ruleId": "901", "severity": 1, "message": "1074", "line": 25, "column": 17, "nodeType": "903", "messageId": "904", "endLine": 25, "endColumn": 29}, {"ruleId": "901", "severity": 1, "message": "1075", "line": 33, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 33, "endColumn": 29}, {"ruleId": "901", "severity": 1, "message": "1076", "line": 3, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 3, "endColumn": 6}, {"ruleId": "901", "severity": 1, "message": "928", "line": 9, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 9, "endColumn": 10}, {"ruleId": "901", "severity": 1, "message": "1036", "line": 58, "column": 15, "nodeType": "903", "messageId": "904", "endLine": 58, "endColumn": 27}, {"ruleId": "965", "severity": 1, "message": "1077", "line": 140, "column": 6, "nodeType": "967", "endLine": 140, "endColumn": 18, "suggestions": "1078"}, {"ruleId": "965", "severity": 1, "message": "1079", "line": 145, "column": 6, "nodeType": "967", "endLine": 145, "endColumn": 52, "suggestions": "1080"}, {"ruleId": "965", "severity": 1, "message": "1081", "line": 150, "column": 6, "nodeType": "967", "endLine": 150, "endColumn": 62, "suggestions": "1082"}, {"ruleId": "965", "severity": 1, "message": "1083", "line": 155, "column": 6, "nodeType": "967", "endLine": 155, "endColumn": 28, "suggestions": "1084"}, {"ruleId": "965", "severity": 1, "message": "1085", "line": 164, "column": 6, "nodeType": "967", "endLine": 164, "endColumn": 39, "suggestions": "1086"}, {"ruleId": "901", "severity": 1, "message": "1087", "line": 39, "column": 13, "nodeType": "903", "messageId": "904", "endLine": 39, "endColumn": 23}, {"ruleId": "965", "severity": 1, "message": "1088", "line": 71, "column": 6, "nodeType": "967", "endLine": 71, "endColumn": 18, "suggestions": "1089"}, {"ruleId": "901", "severity": 1, "message": "970", "line": 1, "column": 27, "nodeType": "903", "messageId": "904", "endLine": 1, "endColumn": 36}, {"ruleId": "901", "severity": 1, "message": "923", "line": 10, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 10, "endColumn": 7}, {"ruleId": "901", "severity": 1, "message": "924", "line": 11, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 11, "endColumn": 14}, {"ruleId": "901", "severity": 1, "message": "940", "line": 12, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 12, "endColumn": 13}, {"ruleId": "901", "severity": 1, "message": "1069", "line": 27, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 27, "endColumn": 8}, {"ruleId": "901", "severity": 1, "message": "931", "line": 30, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 30, "endColumn": 17}, {"ruleId": "901", "severity": 1, "message": "1090", "line": 33, "column": 17, "nodeType": "903", "messageId": "904", "endLine": 33, "endColumn": 25}, {"ruleId": "901", "severity": 1, "message": "913", "line": 34, "column": 17, "nodeType": "903", "messageId": "904", "endLine": 34, "endColumn": 27}, {"ruleId": "901", "severity": 1, "message": "943", "line": 35, "column": 14, "nodeType": "903", "messageId": "904", "endLine": 35, "endColumn": 25}, {"ruleId": "901", "severity": 1, "message": "923", "line": 10, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 10, "endColumn": 7}, {"ruleId": "901", "severity": 1, "message": "924", "line": 11, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 11, "endColumn": 14}, {"ruleId": "901", "severity": 1, "message": "1069", "line": 27, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 27, "endColumn": 8}, {"ruleId": "901", "severity": 1, "message": "1091", "line": 28, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 28, "endColumn": 12}, {"ruleId": "901", "severity": 1, "message": "1092", "line": 29, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 29, "endColumn": 19}, {"ruleId": "901", "severity": 1, "message": "1093", "line": 30, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 30, "endColumn": 19}, {"ruleId": "901", "severity": 1, "message": "931", "line": 34, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 34, "endColumn": 17}, {"ruleId": "901", "severity": 1, "message": "1094", "line": 37, "column": 17, "nodeType": "903", "messageId": "904", "endLine": 37, "endColumn": 31}, {"ruleId": "965", "severity": 1, "message": "1095", "line": 98, "column": 6, "nodeType": "967", "endLine": 98, "endColumn": 24, "suggestions": "1096"}, {"ruleId": "901", "severity": 1, "message": "923", "line": 4, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 4, "endColumn": 7}, {"ruleId": "901", "severity": 1, "message": "924", "line": 5, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 5, "endColumn": 14}, {"ruleId": "901", "severity": 1, "message": "1097", "line": 29, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 29, "endColumn": 15}, {"ruleId": "965", "severity": 1, "message": "1098", "line": 81, "column": 6, "nodeType": "967", "endLine": 81, "endColumn": 25, "suggestions": "1099"}, {"ruleId": "965", "severity": 1, "message": "1100", "line": 88, "column": 6, "nodeType": "967", "endLine": 88, "endColumn": 24, "suggestions": "1101"}, {"ruleId": "901", "severity": 1, "message": "1102", "line": 140, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 140, "endColumn": 19}, {"ruleId": "901", "severity": 1, "message": "1103", "line": 196, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 196, "endColumn": 27}, {"ruleId": "901", "severity": 1, "message": "1104", "line": 233, "column": 11, "nodeType": "903", "messageId": "904", "endLine": 233, "endColumn": 24}, {"ruleId": "965", "severity": 1, "message": "1105", "line": 389, "column": 6, "nodeType": "967", "endLine": 389, "endColumn": 58, "suggestions": "1106"}, {"ruleId": "901", "severity": 1, "message": "1107", "line": 15, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 15, "endColumn": 14}, {"ruleId": "901", "severity": 1, "message": "1072", "line": 16, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 16, "endColumn": 13}, {"ruleId": "901", "severity": 1, "message": "1071", "line": 17, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 17, "endColumn": 9}, {"ruleId": "901", "severity": 1, "message": "1070", "line": 18, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 18, "endColumn": 11}, {"ruleId": "901", "severity": 1, "message": "1053", "line": 21, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 21, "endColumn": 15}, {"ruleId": "901", "severity": 1, "message": "1108", "line": 28, "column": 12, "nodeType": "903", "messageId": "904", "endLine": 28, "endColumn": 21}, {"ruleId": "901", "severity": 1, "message": "1042", "line": 33, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 33, "endColumn": 27}, {"ruleId": "901", "severity": 1, "message": "1109", "line": 78, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 78, "endColumn": 24}, {"ruleId": "901", "severity": 1, "message": "982", "line": 8, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 8, "endColumn": 8}, {"ruleId": "901", "severity": 1, "message": "983", "line": 9, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 9, "endColumn": 12}, {"ruleId": "901", "severity": 1, "message": "984", "line": 10, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 10, "endColumn": 12}, {"ruleId": "901", "severity": 1, "message": "985", "line": 11, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 11, "endColumn": 17}, {"ruleId": "901", "severity": 1, "message": "986", "line": 12, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 12, "endColumn": 12}, {"ruleId": "901", "severity": 1, "message": "987", "line": 13, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 13, "endColumn": 11}, {"ruleId": "901", "severity": 1, "message": "1069", "line": 15, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 15, "endColumn": 8}, {"ruleId": "901", "severity": 1, "message": "928", "line": 25, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 25, "endColumn": 10}, {"ruleId": "901", "severity": 1, "message": "1053", "line": 30, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 30, "endColumn": 15}, {"ruleId": "901", "severity": 1, "message": "1024", "line": 32, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 32, "endColumn": 10}, {"ruleId": "901", "severity": 1, "message": "1000", "line": 33, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 33, "endColumn": 8}, {"ruleId": "901", "severity": 1, "message": "1001", "line": 40, "column": 13, "nodeType": "903", "messageId": "904", "endLine": 40, "endColumn": 23}, {"ruleId": "901", "severity": 1, "message": "953", "line": 42, "column": 15, "nodeType": "903", "messageId": "904", "endLine": 42, "endColumn": 27}, {"ruleId": "901", "severity": 1, "message": "1037", "line": 50, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 50, "endColumn": 15}, {"ruleId": "901", "severity": 1, "message": "1038", "line": 51, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 51, "endColumn": 14}, {"ruleId": "901", "severity": 1, "message": "1039", "line": 52, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 52, "endColumn": 22}, {"ruleId": "901", "severity": 1, "message": "1040", "line": 53, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 53, "endColumn": 21}, {"ruleId": "901", "severity": 1, "message": "1041", "line": 54, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 54, "endColumn": 17}, {"ruleId": "901", "severity": 1, "message": "1110", "line": 55, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 55, "endColumn": 15}, {"ruleId": "901", "severity": 1, "message": "1111", "line": 56, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 56, "endColumn": 19}, {"ruleId": "901", "severity": 1, "message": "1112", "line": 57, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 57, "endColumn": 21}, {"ruleId": "901", "severity": 1, "message": "1042", "line": 58, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 58, "endColumn": 20}, {"ruleId": "965", "severity": 1, "message": "1113", "line": 96, "column": 6, "nodeType": "967", "endLine": 96, "endColumn": 32, "suggestions": "1114"}, {"ruleId": "901", "severity": 1, "message": "1115", "line": 223, "column": 13, "nodeType": "903", "messageId": "904", "endLine": 223, "endColumn": 19}, {"ruleId": "901", "severity": 1, "message": "1074", "line": 21, "column": 17, "nodeType": "903", "messageId": "904", "endLine": 21, "endColumn": 29}, {"ruleId": "901", "severity": 1, "message": "970", "line": 1, "column": 27, "nodeType": "903", "messageId": "904", "endLine": 1, "endColumn": 36}, {"ruleId": "901", "severity": 1, "message": "1116", "line": 51, "column": 10, "nodeType": "903", "messageId": "904", "endLine": 51, "endColumn": 30}, {"ruleId": "901", "severity": 1, "message": "1117", "line": 52, "column": 29, "nodeType": "903", "messageId": "904", "endLine": 52, "endColumn": 49}, {"ruleId": "901", "severity": 1, "message": "1118", "line": 242, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 242, "endColumn": 36}, {"ruleId": "901", "severity": 1, "message": "1119", "line": 246, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 246, "endColumn": 38}, {"ruleId": "901", "severity": 1, "message": "1120", "line": 75, "column": 9, "nodeType": "903", "messageId": "904", "endLine": 75, "endColumn": 25}, {"ruleId": "901", "severity": 1, "message": "1121", "line": 123, "column": 11, "nodeType": "903", "messageId": "904", "endLine": 123, "endColumn": 22}, {"ruleId": "1122", "severity": 1, "message": "1123", "line": 126, "column": 5, "nodeType": "1124", "messageId": "1125", "endLine": 201, "endColumn": 6}, {"ruleId": "1122", "severity": 1, "message": "1123", "line": 219, "column": 5, "nodeType": "1124", "messageId": "1125", "endLine": 279, "endColumn": 6}, {"ruleId": "1122", "severity": 1, "message": "1123", "line": 290, "column": 5, "nodeType": "1124", "messageId": "1125", "endLine": 336, "endColumn": 6}, {"ruleId": "1126", "severity": 1, "message": "1127", "line": 429, "column": 1, "nodeType": "1128", "endLine": 429, "endColumn": 47}, {"ruleId": "1129", "severity": 1, "message": "1130", "line": 146, "column": 25, "nodeType": "1131", "messageId": "1132", "endLine": 146, "endColumn": 26, "suggestions": "1133"}, {"ruleId": "1129", "severity": 1, "message": "1134", "line": 146, "column": 37, "nodeType": "1131", "messageId": "1132", "endLine": 146, "endColumn": 38, "suggestions": "1135"}, {"ruleId": "1129", "severity": 1, "message": "1136", "line": 146, "column": 39, "nodeType": "1131", "messageId": "1132", "endLine": 146, "endColumn": 40, "suggestions": "1137"}, {"ruleId": "901", "severity": 1, "message": "923", "line": 4, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 4, "endColumn": 7}, {"ruleId": "901", "severity": 1, "message": "924", "line": 5, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 5, "endColumn": 14}, {"ruleId": "901", "severity": 1, "message": "982", "line": 8, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 8, "endColumn": 8}, {"ruleId": "901", "severity": 1, "message": "983", "line": 9, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 9, "endColumn": 12}, {"ruleId": "901", "severity": 1, "message": "984", "line": 10, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 10, "endColumn": 12}, {"ruleId": "901", "severity": 1, "message": "985", "line": 11, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 11, "endColumn": 17}, {"ruleId": "901", "severity": 1, "message": "986", "line": 12, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 12, "endColumn": 12}, {"ruleId": "901", "severity": 1, "message": "987", "line": 13, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 13, "endColumn": 11}, {"ruleId": "901", "severity": 1, "message": "941", "line": 14, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 14, "endColumn": 8}, {"ruleId": "901", "severity": 1, "message": "928", "line": 31, "column": 3, "nodeType": "903", "messageId": "904", "endLine": 31, "endColumn": 10}, {"ruleId": "965", "severity": 1, "message": "1138", "line": 64, "column": 6, "nodeType": "967", "endLine": 64, "endColumn": 24, "suggestions": "1139"}, {"ruleId": "965", "severity": 1, "message": "1140", "line": 82, "column": 6, "nodeType": "967", "endLine": 82, "endColumn": 18, "suggestions": "1141"}, "no-unused-vars", "'CardActions' is defined but never used.", "Identifier", "unusedVar", "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "'Avatar' is defined but never used.", "'AdminIcon' is defined but never used.", "'ConstructionIcon' is defined but never used.", "'CableIcon' is defined but never used.", "'ReportIcon' is defined but never used.", "'setOpenEliminaCavoDialog' is assigned a value but never used.", "'setOpenModificaCavoDialog' is assigned a value but never used.", "'homeAnchorEl' is assigned a value but never used.", "'adminAnchorEl' is assigned a value but never used.", "'cantieriAnchorEl' is assigned a value but never used.", "'caviAnchorEl' is assigned a value but never used.", "'selectedCantiereName' is assigned a value but never used.", "'React' is defined but never used.", "'Grid' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'CardActionArea' is defined but never used.", "'navigateTo' is assigned a value but never used.", "'TextField' is defined but never used.", "'Divider' is defined but never used.", "'HomeIcon' is defined but never used.", "'ViewListIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'EditIcon' is defined but never used.", "'DeleteIcon' is defined but never used.", "'HistoryIcon' is defined but never used.", "'ParcoCavi' is defined but never used.", "'isImpersonating' is assigned a value but never used.", "'handleSuccess' is assigned a value but never used.", "'handleError' is assigned a value but never used.", "'lastCheck' is assigned a value but never used.", "'Typography' is defined but never used.", "'Paper' is defined but never used.", "'IconButton' is defined but never used.", "'RefreshIcon' is defined but never used.", "'AdminHomeButton' is defined but never used.", "'cantiereName' is assigned a value but never used.", "'LinearProgress' is defined but never used.", "'InfoIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'LinkOffIcon' is defined but never used.", "'TimelineIcon' is defined but never used.", "'CheckBoxIcon' is defined but never used.", "'CheckBoxOutlineBlankIcon' is defined but never used.", "'SettingsIcon' is defined but never used.", "'parcoCaviService' is defined but never used.", "'CavoForm' is defined but never used.", "'openEliminaCavoDialog' is assigned a value but never used.", "'openModificaCavoDialog' is assigned a value but never used.", "'openAggiungiCavoDialog' is assigned a value but never used.", "'setOpenAggiungiCavoDialog' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "'statiInstallazione' is assigned a value but never used.", "'tipologieCavi' is assigned a value but never used.", "'setTipologieCavi' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'calculateStatistics', 'caviAttivi', 'caviSpare', 'error', and 'user'. Either include them or remove the dependency array.", "ArrayExpression", ["1142"], "'handleCreateCommandError' is assigned a value but never used.", "'useEffect' is defined but never used.", "'useAuth' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadStoricoBobine'. Either include it or remove the dependency array.", ["1143"], "'handleBackToCantieri' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array.", ["1144"], "'axios' is defined but never used.", "'API_URL' is assigned a value but never used.", "'filePath' is assigned a value but never used.", "'ListItemIcon' is defined but never used.", "'ListItemButton' is defined but never used.", "'Table' is defined but never used.", "'TableBody' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'WarningIcon' is defined but never used.", "'isEmpty' is defined but never used.", "'isFirstInsertion' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array.", ["1145"], "'renderBobineCards' is assigned a value but never used.", "'token' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'updateCavoForCompatibility'.", "ObjectExpression", "unexpected", "Duplicate key 'getRevisioneCorrente'.", "'Stack' is defined but never used.", "'CancelIcon' is defined but never used.", "'handleCancel' is assigned a value but never used.", "'Button' is defined but never used.", "'ClearIcon' is defined but never used.", "'RulerIcon' is defined but never used.", "'StartIcon' is defined but never used.", "'formatDate' is defined but never used.", "'handleClearSelection' is assigned a value but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'Legend' is defined but never used.", "'progressData' is assigned a value but never used.", "'caviData' is assigned a value but never used.", "'metricsData' is assigned a value but never used.", "'CustomTooltip' is assigned a value but never used.", "'renderCustomizedLabel' is assigned a value but never used.", "'LineChart' is defined but never used.", "'XAxis' is defined but never used.", "'YAxis' is defined but never used.", "'CartesianGrid' is defined but never used.", "'Tooltip' is defined but never used.", "'ResponsiveContainer' is defined but never used.", "'ComposedChart' is defined but never used.", "'Line' is defined but never used.", "'Chip' is defined but never used.", "'bobineData' is assigned a value but never used.", "'totaliData' is assigned a value but never used.", "'analisiData' is assigned a value but never used.", "'isCompleto' is assigned a value but never used.", "'isInCorso' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadCavi' and 'loadWeatherData'. Either include them or remove the dependency array.", ["1146"], "'DownloadIcon' is defined but never used.", "'CABLE_STATES' is defined but never used.", "'REEL_STATES' is defined but never used.", "'determineCableState' is defined but never used.", "'determineReelState' is defined but never used.", "'canModifyCable' is defined but never used.", "'getReelStateColor' is defined but never used.", "'redirectToVisualizzaCavi' is defined but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadBobine' and 'loadCavi'. Either include them or remove the dependency array.", ["1147"], "React Hook useEffect has a missing dependency: 'filterCompatibleBobine'. Either include it or remove the dependency array.", ["1148"], "'handleBackToSelection' is assigned a value but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'internalSelectedCavo' is assigned a value but never used.", "'openDialog' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["1149"], "'latoPartenzaCollegato' is assigned a value but never used.", "'latoArrivoCollegato' is assigned a value but never used.", "'config' is defined but never used.", "'sentData' is assigned a value but never used.", ["1150"], "'result' is assigned a value but never used.", "'hasMetri' is assigned a value but never used.", "'Alert' is defined but never used.", "'MenuItem' is defined but never used.", "'Select' is defined but never used.", "'InputLabel' is defined but never used.", "'filteredCantieri' is assigned a value but never used.", "'LocationIcon' is defined but never used.", "'currentHoldDuration' is assigned a value but never used.", "'Box' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array.", ["1151"], "React Hook useEffect has a missing dependency: 'filterCavi'. Either include it or remove the dependency array.", ["1152"], "React Hook useEffect has a missing dependency: 'filterCertificazioni'. Either include it or remove the dependency array.", ["1153"], "React Hook useEffect has a missing dependency: 'calculateStatistics'. Either include it or remove the dependency array.", ["1154"], "React Hook useEffect has missing dependencies: 'filterCavi' and 'filterCertificazioni'. Either include them or remove the dependency array.", ["1155"], "'PeopleIcon' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadComande' and 'loadStatistiche'. Either include them or remove the dependency array.", ["1156"], "'ViewIcon' is defined but never used.", "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadProve'. Either include it or remove the dependency array.", ["1157"], "'Autocomplete' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadCaviDisponibili'. Either include it or remove the dependency array.", ["1158"], "React Hook useEffect has a missing dependency: 'loadResponsabiliDisponibili'. Either include it or remove the dependency array.", ["1159"], "'handleBack' is assigned a value but never used.", "'matchesNumericTerm' is assigned a value but never used.", "'isNumericTerm' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'cavoMatchesTerm'. Either include it or remove the dependency array.", ["1160"], "'FormControl' is defined but never used.", "'CloseIcon' is defined but never used.", "'getBobinaNumber' is assigned a value but never used.", "'isCableSpare' is defined but never used.", "'isCableInstalled' is defined but never used.", "'getCableStateColor' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array.", ["1161"], "'bobina' is assigned a value but never used.", "'showValidationDialog' is assigned a value but never used.", "'setValidationLoading' is assigned a value but never used.", "'handleValidationDialogClose' is assigned a value but never used.", "'handleValidationDialogProceed' is assigned a value but never used.", "'getSeverityColor' is assigned a value but never used.", "'isConnected' is assigned a value but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", "no-useless-escape", "Unnecessary escape character: \\+.", "Literal", "unnecessaryEscape", ["1162", "1163"], "Unnecessary escape character: \\(.", ["1164", "1165"], "Unnecessary escape character: \\).", ["1166", "1167"], "React Hook useEffect has a missing dependency: 'loadResponsabili'. Either include it or remove the dependency array.", ["1168"], "React Hook useEffect has missing dependencies: 'loadComande', 'loadResponsabili', and 'loadStatistiche'. Either include them or remove the dependency array.", ["1169"], {"desc": "1170", "fix": "1171"}, {"desc": "1172", "fix": "1173"}, {"desc": "1174", "fix": "1175"}, {"desc": "1176", "fix": "1177"}, {"desc": "1178", "fix": "1179"}, {"desc": "1180", "fix": "1181"}, {"desc": "1182", "fix": "1183"}, {"desc": "1184", "fix": "1185"}, {"desc": "1186", "fix": "1187"}, {"desc": "1188", "fix": "1189"}, {"desc": "1190", "fix": "1191"}, {"desc": "1192", "fix": "1193"}, {"desc": "1194", "fix": "1195"}, {"desc": "1196", "fix": "1197"}, {"desc": "1198", "fix": "1199"}, {"desc": "1200", "fix": "1201"}, {"desc": "1202", "fix": "1203"}, {"desc": "1204", "fix": "1205"}, {"desc": "1206", "fix": "1207"}, {"desc": "1208", "fix": "1209"}, {"messageId": "1210", "fix": "1211", "desc": "1212"}, {"messageId": "1213", "fix": "1214", "desc": "1215"}, {"messageId": "1210", "fix": "1216", "desc": "1212"}, {"messageId": "1213", "fix": "1217", "desc": "1215"}, {"messageId": "1210", "fix": "1218", "desc": "1212"}, {"messageId": "1213", "fix": "1219", "desc": "1215"}, {"desc": "1220", "fix": "1221"}, {"desc": "1222", "fix": "1223"}, "Update the dependencies array to be: [calculateStatistics, caviAttivi, caviSpare, error, filters, user]", {"range": "1224", "text": "1225"}, "Update the dependencies array to be: [cantiereId, loadStoricoBobine, selectedReportType]", {"range": "1226", "text": "1227"}, "Update the dependencies array to be: [cantiereId, selectCantiere]", {"range": "1228", "text": "1229"}, "Update the dependencies array to be: [handleOptionSelect, initialOption, loadBobine]", {"range": "1230", "text": "1231"}, "Update the dependencies array to be: [certificazione, cantiereId, loadCavi, loadWeatherData]", {"range": "1232", "text": "1233"}, "Update the dependencies array to be: [cantiereId, loadBobine, loadCavi]", {"range": "1234", "text": "1235"}, "Update the dependencies array to be: [selectedCavo, bobine, filterCompatibleBobine]", {"range": "1236", "text": "1237"}, "Update the dependencies array to be: [cantiereId, loadCavi]", {"range": "1238", "text": "1239"}, "Update the dependencies array to be: [open, bobina, cantiereId, loadCavi]", {"range": "1240", "text": "1241"}, "Update the dependencies array to be: [cantiereId, loadInitialData]", {"range": "1242", "text": "1243"}, "Update the dependencies array to be: [cavi, searchTerm, filters, sortBy, sortOrder, filterCavi]", {"range": "1244", "text": "1245"}, "Update the dependencies array to be: [certificazioni, searchTerm, filters, sortBy, sortOrder, filterCertificazioni]", {"range": "1246", "text": "1247"}, "Update the dependencies array to be: [calculateStatistics, cavi, certificazioni]", {"range": "1248", "text": "1249"}, "Update the dependencies array to be: [activeTab, cavi, certificazioni, filterCavi, filterCertificazioni]", {"range": "1250", "text": "1251"}, "Update the dependencies array to be: [cantiereId, loadComande, loadStatistiche]", {"range": "1252", "text": "1253"}, "Update the dependencies array to be: [certificazioneId, loadProve]", {"range": "1254", "text": "1255"}, "Update the dependencies array to be: [loadCaviDisponibili, open, tipoComanda]", {"range": "1256", "text": "1257"}, "Update the dependencies array to be: [open, cantiereId, loadResponsabiliDisponibili]", {"range": "1258", "text": "1259"}, "Update the dependencies array to be: [searchText, searchType, cavi, onFilteredDataChange, cavoMatchesTerm]", {"range": "1260", "text": "1261"}, "Update the dependencies array to be: [open, cavoPreselezionato, loadBobine]", {"range": "1262", "text": "1263"}, "removeEscape", {"range": "1264", "text": "1265"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1266", "text": "1267"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "1268", "text": "1265"}, {"range": "1269", "text": "1267"}, {"range": "1270", "text": "1265"}, {"range": "1271", "text": "1267"}, "Update the dependencies array to be: [open, cantiereId, loadResponsabili]", {"range": "1272", "text": "1273"}, "Update the dependencies array to be: [cantiereId, loadComande, loadResponsabili, loadStatistiche]", {"range": "1274", "text": "1275"}, [27032, 27041], "[calculateStatistics, caviAttivi, caviSpare, error, filters, user]", [5510, 5542], "[cantiere<PERSON>d, loadStoricoBobine, selectedReportType]", [1559, 1571], "[cantiereId, selectCantiere]", [5793, 5795], "[handleOptionSelect, initialOption, loadBobine]", [1809, 1837], "[certificazione, cantiereId, loadCavi, loadWeatherData]", [2572, 2584], "[cantiereId, loadBobine, loadCavi]", [14450, 14472], "[selectedCavo, bobine, filterCompatibleBobine]", [1077, 1089], "[cantiereId, loadCavi]", [2734, 2760], "[open, bobina, cantiereId, loadCavi]", [3803, 3815], "[cantiereId, loadInitialData]", [3900, 3946], "[cavi, searchTerm, filters, sortBy, sortOrder, filterCavi]", [4030, 4086], "[certificazioni, searchTerm, filters, sortBy, sortOrder, filterCertificazioni]", [4192, 4214], "[calculateStatistics, cavi, certificazioni]", [4436, 4469], "[activeTab, cavi, certificazioni, filterCavi, filterCertificazioni]", [1672, 1684], "[cantiereId, loadComande, loadStatistiche]", [2516, 2534], "[certificazioneId, loadProve]", [1967, 1986], "[loadCaviDisponibili, open, tipoComanda]", [2150, 2168], "[open, cantiereId, loadResponsabiliDisponibili]", [11274, 11326], "[searchText, searchType, cavi, onFilteredDataChange, cavoMatchesTerm]", [2440, 2466], "[open, cavoPreselezionato, loadBobine]", [4732, 4733], "", [4732, 4732], "\\", [4744, 4745], [4744, 4744], [4746, 4747], [4746, 4746], [1518, 1536], "[open, cantiereId, loadResponsabili]", [2107, 2119], "[cantiereId, loadComande, loadResponsabili, loadStatistiche]"]