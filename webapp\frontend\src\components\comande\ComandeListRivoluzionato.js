import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  Tooltip,
  Grid,
  List,
  ListItem,
  ListItemText,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Assignment as AssignIcon,
  Refresh as RefreshIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import comandeService from '../../services/comandeService';
import responsabiliService from '../../services/responsabiliService';
import CreaComandaConCavi from './CreaComandaConCavi';

const ComandeListRivoluzionato = ({ cantiereId, cantiereName }) => {
  // Stati principali
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Stati comande
  const [comande, setComande] = useState([]);
  const [statistiche, setStatistiche] = useState(null);
  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);

  // Stati responsabili
  const [responsabili, setResponsabili] = useState([]);
  const [loadingResponsabili, setLoadingResponsabili] = useState(false);
  const [comandePerResponsabile, setComandePerResponsabile] = useState({});
  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);
  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');
  const [selectedResponsabile, setSelectedResponsabile] = useState(null);
  const [formDataResponsabile, setFormDataResponsabile] = useState({
    nome_responsabile: '',
    email: '',
    telefono: ''
  });

  // Carica dati al mount
  useEffect(() => {
    if (cantiereId) {
      loadComande();
      loadStatistiche();
      loadResponsabili();
    }
  }, [cantiereId]);

  const loadComande = async () => {
    try {
      setLoading(true);
      const data = await comandeService.getComande(cantiereId);
      setComande(data.comande || []);
      setError(null);
    } catch (err) {
      console.error('Errore nel caricamento delle comande:', err);
      setError('Errore nel caricamento delle comande');
    } finally {
      setLoading(false);
    }
  };

  const loadStatistiche = async () => {
    try {
      const stats = await comandeService.getStatisticheComande(cantiereId);
      setStatistiche(stats);
    } catch (err) {
      console.error('Errore nel caricamento delle statistiche:', err);
    }
  };

  const loadResponsabili = async () => {
    try {
      setLoadingResponsabili(true);
      setError(null);

      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);
      setResponsabili(data || []);
      await loadComandePerResponsabili(data || []);
    } catch (err) {
      console.error('Errore nel caricamento dei responsabili:', err);
      const errorMessage = err.response?.data?.detail || err.message || 'Errore nel caricamento dei responsabili';
      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);
    } finally {
      setLoadingResponsabili(false);
    }
  };

  const loadComandePerResponsabili = async (responsabiliList) => {
    try {
      const comandeMap = {};
      for (const responsabile of responsabiliList) {
        try {
          const response = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);
          // Assicurati che sia sempre un array
          let comande = [];
          if (response && Array.isArray(response)) {
            comande = response;
          } else if (response && response.comande && Array.isArray(response.comande)) {
            comande = response.comande;
          } else if (response && response.data && Array.isArray(response.data)) {
            comande = response.data;
          }
          comandeMap[responsabile.id_responsabile] = comande;
        } catch (err) {
          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);
          comandeMap[responsabile.id_responsabile] = [];
        }
      }
      setComandePerResponsabile(comandeMap);
    } catch (err) {
      console.error('Errore nel caricamento delle comande:', err);
    }
  };

  // Gestione responsabili
  const handleOpenResponsabileDialog = (mode, responsabile = null) => {
    setDialogModeResponsabile(mode);
    setSelectedResponsabile(responsabile);
    
    if (mode === 'edit' && responsabile) {
      setFormDataResponsabile({
        nome_responsabile: responsabile.nome_responsabile || '',
        email: responsabile.email || '',
        telefono: responsabile.telefono || ''
      });
    } else {
      setFormDataResponsabile({
        nome_responsabile: '',
        email: '',
        telefono: ''
      });
    }
    
    setOpenResponsabileDialog(true);
  };

  const handleCloseResponsabileDialog = () => {
    setOpenResponsabileDialog(false);
    setSelectedResponsabile(null);
    setError(null);
  };

  const handleSubmitResponsabile = async () => {
    try {
      setError(null);
      
      if (!formDataResponsabile.nome_responsabile.trim()) {
        setError('Il nome del responsabile è obbligatorio');
        return;
      }
      
      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {
        setError('Almeno uno tra email e telefono deve essere specificato');
        return;
      }

      if (dialogModeResponsabile === 'create') {
        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);
      } else if (dialogModeResponsabile === 'edit') {
        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);
      }

      handleCloseResponsabileDialog();
      await loadResponsabili();
    } catch (err) {
      console.error('Errore nel salvataggio:', err);
      setError(err.detail || 'Errore nel salvataggio del responsabile');
    }
  };

  const handleDeleteResponsabile = async (idResponsabile) => {
    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {
      return;
    }

    try {
      await responsabiliService.deleteResponsabile(idResponsabile);
      await loadResponsabili();
    } catch (err) {
      console.error('Errore nell\'eliminazione:', err);
      setError('Errore nell\'eliminazione del responsabile');
    }
  };

  const getTipoComandaLabel = (tipo) => {
    const labels = {
      'POSA': 'Posa',
      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',
      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',
      'CERTIFICAZIONE': 'Certificazione'
    };
    return labels[tipo] || tipo;
  };

  const getStatoColor = (stato) => {
    const colors = {
      'CREATA': 'default',
      'IN_CORSO': 'primary',
      'COMPLETATA': 'success',
      'ANNULLATA': 'error'
    };
    return colors[stato] || 'default';
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom sx={{ fontWeight: 500, color: 'text.primary' }}>
        Gestione Comande
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Tabs per navigazione */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 4 }}>
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          sx={{
            '& .MuiTab-root': {
              textTransform: 'none',
              fontWeight: 500,
              fontSize: '1rem'
            }
          }}
        >
          <Tab label="Responsabili" />
          <Tab label="Tutte le Comande" />
        </Tabs>
      </Box>

      {/* Tab 0: Responsabili */}
      {activeTab === 0 && (
        <Box>
          {/* Toolbar Responsabili */}
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h5" sx={{ fontWeight: 500, color: 'text.primary' }}>
              Responsabili del Cantiere
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => handleOpenResponsabileDialog('create')}
              sx={{
                textTransform: 'none',
                fontWeight: 500,
                px: 3,
                py: 1
              }}
            >
              Inserisci Responsabile
            </Button>
          </Box>

          {loadingResponsabili ? (
            <Box display="flex" justifyContent="center" py={4}>
              <CircularProgress />
            </Box>
          ) : (
            <Box>
              {responsabili.length === 0 ? (
                <Paper
                  elevation={0}
                  sx={{
                    p: 6,
                    textAlign: 'center',
                    backgroundColor: 'grey.50',
                    border: '1px dashed',
                    borderColor: 'grey.300'
                  }}
                >
                  <PersonIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    Nessun responsabile configurato
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Aggiungi il primo responsabile per iniziare a gestire le comande
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => handleOpenResponsabileDialog('create')}
                    sx={{ textTransform: 'none' }}
                  >
                    Inserisci Primo Responsabile
                  </Button>
                </Paper>
              ) : (
                responsabili.map((responsabile) => (
                  <Accordion
                    key={responsabile.id_responsabile}
                    sx={{
                      mb: 2,
                      '&:before': { display: 'none' },
                      boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                      border: '1px solid',
                      borderColor: 'grey.200'
                    }}
                  >
                    <AccordionSummary
                      expandIcon={<ExpandMoreIcon />}
                      sx={{
                        '&:hover': {
                          backgroundColor: 'grey.50'
                        }
                      }}
                    >
                      <Box display="flex" alignItems="center" justifyContent="space-between" width="100%">
                        <Box display="flex" alignItems="center" gap={2}>
                          <PersonIcon color="primary" sx={{ fontSize: 28 }} />
                          <Box>
                            <Typography variant="h6" sx={{ fontWeight: 500 }}>
                              {responsabile.nome_responsabile}
                            </Typography>
                            <Box display="flex" gap={3} mt={0.5}>
                              {responsabile.email && (
                                <Box display="flex" alignItems="center" gap={0.5}>
                                  <EmailIcon fontSize="small" color="action" />
                                  <Typography variant="body2" color="text.secondary">
                                    {responsabile.email}
                                  </Typography>
                                </Box>
                              )}
                              {responsabile.telefono && (
                                <Box display="flex" alignItems="center" gap={0.5}>
                                  <PhoneIcon fontSize="small" color="action" />
                                  <Typography variant="body2" color="text.secondary">
                                    {responsabile.telefono}
                                  </Typography>
                                </Box>
                              )}
                            </Box>
                          </Box>
                        </Box>

                        <Box display="flex" alignItems="center" gap={1} onClick={(e) => e.stopPropagation()}>
                          <Chip
                            icon={<AssignIcon />}
                            label={`${Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) ? comandePerResponsabile[responsabile.id_responsabile].length : 0} comande`}
                            size="small"
                            color="primary"
                            variant="outlined"
                            sx={{ fontWeight: 500 }}
                          />
                          <Tooltip title="Modifica responsabile">
                            <IconButton
                              size="small"
                              onClick={() => handleOpenResponsabileDialog('edit', responsabile)}
                              sx={{
                                '&:hover': {
                                  backgroundColor: 'primary.light',
                                  color: 'white'
                                }
                              }}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Elimina responsabile">
                            <IconButton
                              size="small"
                              onClick={() => handleDeleteResponsabile(responsabile.id_responsabile)}
                              sx={{
                                '&:hover': {
                                  backgroundColor: 'error.light',
                                  color: 'white'
                                }
                              }}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </Box>
                    </AccordionSummary>

                    <AccordionDetails sx={{ pt: 2 }}>
                      <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                        Comande Assegnate
                      </Typography>

                      {(!Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) || comandePerResponsabile[responsabile.id_responsabile].length === 0) ? (
                        <Box
                          sx={{
                            p: 3,
                            textAlign: 'center',
                            backgroundColor: 'grey.50',
                            borderRadius: 1,
                            border: '1px dashed',
                            borderColor: 'grey.300'
                          }}
                        >
                          <Typography variant="body2" color="text.secondary">
                            Nessuna comanda assegnata a questo responsabile
                          </Typography>
                        </Box>
                      ) : (
                        <List dense>
                          {Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) && comandePerResponsabile[responsabile.id_responsabile].map((comanda) => (
                            <ListItem key={comanda.codice_comanda} divider>
                              <ListItemText
                                primary={
                                  <Box display="flex" alignItems="center" gap={1}>
                                    <Typography variant="body2" fontWeight="bold">
                                      {comanda.codice_comanda}
                                    </Typography>
                                    <Chip
                                      label={getTipoComandaLabel(comanda.tipo_comanda)}
                                      size="small"
                                      variant="outlined"
                                    />
                                    <Chip
                                      label={comanda.stato || 'CREATA'}
                                      size="small"
                                      color={getStatoColor(comanda.stato)}
                                    />
                                  </Box>
                                }
                                secondary={
                                  <Typography variant="body2" color="textSecondary">
                                    {comanda.descrizione || 'Nessuna descrizione'}
                                    {comanda.data_creazione && ` • Creata: ${new Date(comanda.data_creazione).toLocaleDateString()}`}
                                  </Typography>
                                }
                              />
                            </ListItem>
                          ))}
                        </List>
                      )}
                    </AccordionDetails>
                  </Accordion>
                ))
              )}
            </Box>
          )}
        </Box>
      )}

      {/* Tab 1: Tutte le Comande */}
      {activeTab === 1 && (
        <Box>
          {/* Statistiche */}
          {statistiche && (
            <Grid container spacing={3} sx={{ mb: 4 }}>
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ boxShadow: '0 1px 3px rgba(0,0,0,0.1)', border: '1px solid', borderColor: 'grey.200' }}>
                  <CardContent sx={{ textAlign: 'center', py: 3 }}>
                    <Typography color="text.secondary" variant="body2" sx={{ fontWeight: 500, mb: 1 }}>
                      Totale
                    </Typography>
                    <Typography variant="h4" sx={{ fontWeight: 600, color: 'text.primary' }}>
                      {statistiche.totale_comande}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ boxShadow: '0 1px 3px rgba(0,0,0,0.1)', border: '1px solid', borderColor: 'grey.200' }}>
                  <CardContent sx={{ textAlign: 'center', py: 3 }}>
                    <Typography color="text.secondary" variant="body2" sx={{ fontWeight: 500, mb: 1 }}>
                      Create
                    </Typography>
                    <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
                      {statistiche.comande_create}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ boxShadow: '0 1px 3px rgba(0,0,0,0.1)', border: '1px solid', borderColor: 'grey.200' }}>
                  <CardContent sx={{ textAlign: 'center', py: 3 }}>
                    <Typography color="text.secondary" variant="body2" sx={{ fontWeight: 500, mb: 1 }}>
                      In Corso
                    </Typography>
                    <Typography variant="h4" sx={{ fontWeight: 600, color: 'warning.main' }}>
                      {statistiche.comande_in_corso}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{ boxShadow: '0 1px 3px rgba(0,0,0,0.1)', border: '1px solid', borderColor: 'grey.200' }}>
                  <CardContent sx={{ textAlign: 'center', py: 3 }}>
                    <Typography color="text.secondary" variant="body2" sx={{ fontWeight: 500, mb: 1 }}>
                      Completate
                    </Typography>
                    <Typography variant="h4" sx={{ fontWeight: 600, color: 'success.main' }}>
                      {statistiche.comande_completate}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {/* Toolbar Comande */}
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h5" sx={{ fontWeight: 500, color: 'text.primary' }}>
              Tutte le Comande
            </Typography>
            <Box display="flex" gap={2}>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setOpenCreaConCavi(true)}
                sx={{
                  textTransform: 'none',
                  fontWeight: 500,
                  px: 3
                }}
              >
                Nuova Comanda
              </Button>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => {
                  loadComande();
                  loadStatistiche();
                }}
                sx={{
                  textTransform: 'none',
                  fontWeight: 500
                }}
              >
                Aggiorna
              </Button>
            </Box>
          </Box>

          {/* Tabella Comande */}
          <TableContainer component={Paper} sx={{ boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: 'grey.50' }}>
                  <TableCell sx={{ fontWeight: 600 }}>Codice</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Tipo</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Responsabile</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Stato</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Priorità</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Data Creazione</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Azioni</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {comande.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center" sx={{ py: 8 }}>
                      <Box>
                        <AssignIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
                        <Typography variant="h6" color="text.secondary" gutterBottom>
                          Nessuna comanda trovata
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                          Crea la prima comanda per iniziare
                        </Typography>
                        <Button
                          variant="contained"
                          startIcon={<AddIcon />}
                          onClick={() => setOpenCreaConCavi(true)}
                          sx={{ textTransform: 'none' }}
                        >
                          Crea Prima Comanda
                        </Button>
                      </Box>
                    </TableCell>
                  </TableRow>
                ) : (
                  comande.map((comanda) => (
                    <TableRow key={comanda.codice_comanda}>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          {comanda.codice_comanda}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={getTipoComandaLabel(comanda.tipo_comanda)}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {comanda.responsabile || 'Non assegnato'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={comanda.stato || 'CREATA'}
                          size="small"
                          color={getStatoColor(comanda.stato)}
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={comanda.priorita || 'NORMALE'}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {comanda.data_creazione ? new Date(comanda.data_creazione).toLocaleDateString() : '-'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box display="flex" gap={0.5}>
                          <Tooltip title="Visualizza">
                            <IconButton size="small">
                              <ViewIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Modifica">
                            <IconButton size="small">
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Elimina">
                            <IconButton size="small" color="error">
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      )}

      {/* Dialog per creazione/modifica responsabile */}
      <Dialog
        open={openResponsabileDialog}
        onClose={handleCloseResponsabileDialog}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: { borderRadius: 2 }
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            {dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'}
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="Nome Responsabile"
              value={formDataResponsabile.nome_responsabile}
              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, nome_responsabile: e.target.value })}
              margin="normal"
              required
              variant="outlined"
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="Email"
              type="email"
              value={formDataResponsabile.email}
              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, email: e.target.value })}
              margin="normal"
              variant="outlined"
              helperText="Email per notifiche (opzionale se inserisci telefono)"
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="Telefono"
              value={formDataResponsabile.telefono}
              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, telefono: e.target.value })}
              margin="normal"
              variant="outlined"
              helperText="Numero per SMS (opzionale se inserisci email)"
            />
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 2 }}>
          <Button
            onClick={handleCloseResponsabileDialog}
            sx={{ textTransform: 'none' }}
          >
            Annulla
          </Button>
          <Button
            onClick={handleSubmitResponsabile}
            variant="contained"
            sx={{
              textTransform: 'none',
              fontWeight: 500,
              px: 3
            }}
          >
            {dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog CreaComandaConCavi */}
      <CreaComandaConCavi
        cantiereId={cantiereId}
        open={openCreaConCavi}
        onClose={() => setOpenCreaConCavi(false)}
        onSuccess={() => {
          loadComande();
          loadStatistiche();
          loadResponsabili();
          setOpenCreaConCavi(false);
        }}
      />
    </Box>
  );
};

export default ComandeListRivoluzionato;
