import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  Tooltip,
  Grid,
  List,
  ListItem,
  ListItemText,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Assignment as AssignIcon,
  Refresh as RefreshIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import comandeService from '../../services/comandeService';
import responsabiliService from '../../services/responsabiliService';
import CreaComandaConCavi from './CreaComandaConCavi';

const ComandeListRivoluzionato = ({ cantiereId, cantiereName }) => {
  // Stati principali
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Stati comande
  const [comande, setComande] = useState([]);
  const [statistiche, setStatistiche] = useState(null);
  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);

  // Stati responsabili
  const [responsabili, setResponsabili] = useState([]);
  const [loadingResponsabili, setLoadingResponsabili] = useState(false);
  const [comandePerResponsabile, setComandePerResponsabile] = useState({});
  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);
  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');
  const [selectedResponsabile, setSelectedResponsabile] = useState(null);
  const [formDataResponsabile, setFormDataResponsabile] = useState({
    nome_responsabile: '',
    email: '',
    telefono: ''
  });

  // Carica dati al mount
  useEffect(() => {
    if (cantiereId) {
      loadComande();
      loadStatistiche();
      loadResponsabili();
    }
  }, [cantiereId]);

  const loadComande = async () => {
    try {
      setLoading(true);
      const data = await comandeService.getComande(cantiereId);
      setComande(data.comande || []);
      setError(null);
    } catch (err) {
      console.error('Errore nel caricamento delle comande:', err);
      setError('Errore nel caricamento delle comande');
    } finally {
      setLoading(false);
    }
  };

  const loadStatistiche = async () => {
    try {
      const stats = await comandeService.getStatisticheComande(cantiereId);
      setStatistiche(stats);
    } catch (err) {
      console.error('Errore nel caricamento delle statistiche:', err);
    }
  };

  const loadResponsabili = async () => {
    try {
      setLoadingResponsabili(true);
      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);
      setResponsabili(data || []);
      await loadComandePerResponsabili(data || []);
    } catch (err) {
      console.error('Errore nel caricamento dei responsabili:', err);
      setError('Errore nel caricamento dei responsabili');
    } finally {
      setLoadingResponsabili(false);
    }
  };

  const loadComandePerResponsabili = async (responsabiliList) => {
    try {
      const comandeMap = {};
      for (const responsabile of responsabiliList) {
        try {
          const comande = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);
          comandeMap[responsabile.id_responsabile] = comande || [];
        } catch (err) {
          comandeMap[responsabile.id_responsabile] = [];
        }
      }
      setComandePerResponsabile(comandeMap);
    } catch (err) {
      console.error('Errore nel caricamento delle comande:', err);
    }
  };

  // Gestione responsabili
  const handleOpenResponsabileDialog = (mode, responsabile = null) => {
    setDialogModeResponsabile(mode);
    setSelectedResponsabile(responsabile);
    
    if (mode === 'edit' && responsabile) {
      setFormDataResponsabile({
        nome_responsabile: responsabile.nome_responsabile || '',
        email: responsabile.email || '',
        telefono: responsabile.telefono || ''
      });
    } else {
      setFormDataResponsabile({
        nome_responsabile: '',
        email: '',
        telefono: ''
      });
    }
    
    setOpenResponsabileDialog(true);
  };

  const handleCloseResponsabileDialog = () => {
    setOpenResponsabileDialog(false);
    setSelectedResponsabile(null);
    setError(null);
  };

  const handleSubmitResponsabile = async () => {
    try {
      setError(null);
      
      if (!formDataResponsabile.nome_responsabile.trim()) {
        setError('Il nome del responsabile è obbligatorio');
        return;
      }
      
      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {
        setError('Almeno uno tra email e telefono deve essere specificato');
        return;
      }

      if (dialogModeResponsabile === 'create') {
        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);
      } else if (dialogModeResponsabile === 'edit') {
        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);
      }

      handleCloseResponsabileDialog();
      await loadResponsabili();
    } catch (err) {
      console.error('Errore nel salvataggio:', err);
      setError(err.detail || 'Errore nel salvataggio del responsabile');
    }
  };

  const handleDeleteResponsabile = async (idResponsabile) => {
    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {
      return;
    }

    try {
      await responsabiliService.deleteResponsabile(idResponsabile);
      await loadResponsabili();
    } catch (err) {
      console.error('Errore nell\'eliminazione:', err);
      setError('Errore nell\'eliminazione del responsabile');
    }
  };

  const getTipoComandaLabel = (tipo) => {
    const labels = {
      'POSA': 'Posa',
      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',
      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',
      'CERTIFICAZIONE': 'Certificazione'
    };
    return labels[tipo] || tipo;
  };

  const getStatoColor = (stato) => {
    const colors = {
      'CREATA': 'default',
      'IN_CORSO': 'primary',
      'COMPLETATA': 'success',
      'ANNULLATA': 'error'
    };
    return colors[stato] || 'default';
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Gestione Comande - {cantiereName}
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Tabs per navigazione */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
          <Tab label="Responsabili" />
          <Tab label="Tutte le Comande" />
        </Tabs>
      </Box>

      {/* Tab 0: Responsabili */}
      {activeTab === 0 && (
        <Box>
          {/* Toolbar Responsabili */}
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h5">
              Responsabili del Cantiere
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => handleOpenResponsabileDialog('create')}
            >
              Inserisci Responsabile
            </Button>
          </Box>

          {loadingResponsabili ? (
            <Box display="flex" justifyContent="center" py={4}>
              <CircularProgress />
            </Box>
          ) : (
            <Box>
              {responsabili.length === 0 ? (
                <Box textAlign="center" py={4}>
                  <Typography variant="h6" color="textSecondary">
                    Nessun responsabile trovato
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Clicca su "Inserisci Responsabile" per iniziare
                  </Typography>
                </Box>
              ) : (
                responsabili.map((responsabile) => (
                  <Accordion key={responsabile.id_responsabile} sx={{ mb: 1 }}>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Box display="flex" alignItems="center" justifyContent="space-between" width="100%">
                        <Box display="flex" alignItems="center" gap={2}>
                          <PersonIcon color="primary" />
                          <Box>
                            <Typography variant="h6">
                              {responsabile.nome_responsabile}
                            </Typography>
                            <Box display="flex" gap={2} mt={0.5}>
                              {responsabile.email && (
                                <Box display="flex" alignItems="center" gap={0.5}>
                                  <EmailIcon fontSize="small" color="action" />
                                  <Typography variant="body2" color="textSecondary">
                                    {responsabile.email}
                                  </Typography>
                                </Box>
                              )}
                              {responsabile.telefono && (
                                <Box display="flex" alignItems="center" gap={0.5}>
                                  <PhoneIcon fontSize="small" color="action" />
                                  <Typography variant="body2" color="textSecondary">
                                    {responsabile.telefono}
                                  </Typography>
                                </Box>
                              )}
                            </Box>
                          </Box>
                        </Box>
                        
                        <Box display="flex" gap={1} onClick={(e) => e.stopPropagation()}>
                          <Chip
                            icon={<AssignIcon />}
                            label={`${(comandePerResponsabile[responsabile.id_responsabile] || []).length} comande`}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                          <Tooltip title="Modifica">
                            <IconButton
                              size="small"
                              onClick={() => handleOpenResponsabileDialog('edit', responsabile)}
                            >
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Elimina">
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleDeleteResponsabile(responsabile.id_responsabile)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </Box>
                    </AccordionSummary>
                    
                    <AccordionDetails>
                      <Typography variant="subtitle2" gutterBottom>
                        Comande Assegnate:
                      </Typography>
                      
                      {(comandePerResponsabile[responsabile.id_responsabile] || []).length === 0 ? (
                        <Typography variant="body2" color="textSecondary" style={{ fontStyle: 'italic' }}>
                          Nessuna comanda assegnata
                        </Typography>
                      ) : (
                        <List dense>
                          {(comandePerResponsabile[responsabile.id_responsabile] || []).map((comanda) => (
                            <ListItem key={comanda.codice_comanda} divider>
                              <ListItemText
                                primary={
                                  <Box display="flex" alignItems="center" gap={1}>
                                    <Typography variant="body2" fontWeight="bold">
                                      {comanda.codice_comanda}
                                    </Typography>
                                    <Chip
                                      label={getTipoComandaLabel(comanda.tipo_comanda)}
                                      size="small"
                                      variant="outlined"
                                    />
                                    <Chip
                                      label={comanda.stato || 'CREATA'}
                                      size="small"
                                      color={getStatoColor(comanda.stato)}
                                    />
                                  </Box>
                                }
                                secondary={
                                  <Typography variant="body2" color="textSecondary">
                                    {comanda.descrizione || 'Nessuna descrizione'}
                                    {comanda.data_creazione && ` • Creata: ${new Date(comanda.data_creazione).toLocaleDateString()}`}
                                  </Typography>
                                }
                              />
                            </ListItem>
                          ))}
                        </List>
                      )}
                    </AccordionDetails>
                  </Accordion>
                ))
              )}
            </Box>
          )}
        </Box>
      )}

      {/* Tab 1: Tutte le Comande */}
      {activeTab === 1 && (
        <Box>
          {/* Statistiche */}
          {statistiche && (
            <Grid container spacing={2} sx={{ mb: 3 }}>
              <Grid item xs={12} sm={6} md={2}>
                <Card>
                  <CardContent>
                    <Typography color="textSecondary" gutterBottom>
                      Totale
                    </Typography>
                    <Typography variant="h5">
                      {statistiche.totale_comande}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <Card>
                  <CardContent>
                    <Typography color="textSecondary" gutterBottom>
                      Create
                    </Typography>
                    <Typography variant="h5" color="primary">
                      {statistiche.comande_create}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <Card>
                  <CardContent>
                    <Typography color="textSecondary" gutterBottom>
                      In Corso
                    </Typography>
                    <Typography variant="h5" color="warning.main">
                      {statistiche.comande_in_corso}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={2}>
                <Card>
                  <CardContent>
                    <Typography color="textSecondary" gutterBottom>
                      Completate
                    </Typography>
                    <Typography variant="h5" color="success.main">
                      {statistiche.comande_completate}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {/* Toolbar Comande */}
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h5">
              Tutte le Comande
            </Typography>
            <Box display="flex" gap={1}>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setOpenCreaConCavi(true)}
              >
                Nuova Comanda
              </Button>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => {
                  loadComande();
                  loadStatistiche();
                }}
              >
                Aggiorna
              </Button>
            </Box>
          </Box>

          {/* Tabella Comande */}
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Codice</TableCell>
                  <TableCell>Tipo</TableCell>
                  <TableCell>Responsabile</TableCell>
                  <TableCell>Stato</TableCell>
                  <TableCell>Priorità</TableCell>
                  <TableCell>Data Creazione</TableCell>
                  <TableCell>Azioni</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {comande.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      <Typography variant="body2" color="textSecondary">
                        Nessuna comanda trovata
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  comande.map((comanda) => (
                    <TableRow key={comanda.codice_comanda}>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          {comanda.codice_comanda}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={getTipoComandaLabel(comanda.tipo_comanda)}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {comanda.responsabile || 'Non assegnato'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={comanda.stato || 'CREATA'}
                          size="small"
                          color={getStatoColor(comanda.stato)}
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={comanda.priorita || 'NORMALE'}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {comanda.data_creazione ? new Date(comanda.data_creazione).toLocaleDateString() : '-'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box display="flex" gap={0.5}>
                          <Tooltip title="Visualizza">
                            <IconButton size="small">
                              <ViewIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Modifica">
                            <IconButton size="small">
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Elimina">
                            <IconButton size="small" color="error">
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      )}

      {/* Dialog per creazione/modifica responsabile */}
      <Dialog open={openResponsabileDialog} onClose={handleCloseResponsabileDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label="Nome Responsabile"
              value={formDataResponsabile.nome_responsabile}
              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, nome_responsabile: e.target.value })}
              margin="normal"
              required
            />

            <TextField
              fullWidth
              label="Email"
              type="email"
              value={formDataResponsabile.email}
              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, email: e.target.value })}
              margin="normal"
              helperText="Email per notifiche (opzionale se inserisci telefono)"
            />

            <TextField
              fullWidth
              label="Telefono"
              value={formDataResponsabile.telefono}
              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, telefono: e.target.value })}
              margin="normal"
              helperText="Numero per SMS (opzionale se inserisci email)"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseResponsabileDialog}>
            Annulla
          </Button>
          <Button onClick={handleSubmitResponsabile} variant="contained">
            {dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog CreaComandaConCavi */}
      <CreaComandaConCavi
        cantiereId={cantiereId}
        open={openCreaConCavi}
        onClose={() => setOpenCreaConCavi(false)}
        onSuccess={() => {
          loadComande();
          loadStatistiche();
          loadResponsabili();
          setOpenCreaConCavi(false);
        }}
      />
    </Box>
  );
};

export default ComandeListRivoluzionato;
